import { memo } from 'react';

import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';

import { LifestyleMode } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';

import FoodIntakePatternModal from '../nutrition/practice/food-intake-patterns-tab/FoodIntakePatternModal';
import AttitudeModal from '../physical-activity/attitude/attitude-tab/AttitudeModal';
import ExercisePatternModal from '../physical-activity/practice/exercise-patterns-tab/ExercisePatternModal';

const {
  NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS,
  PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  PHYSICAL_ACTIVITY_ATTITUDE,
} = LifestyleSources;

const getFormModal = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return <FoodIntakePatternModal mode={LifestyleMode.CREATE} />;
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return <ExercisePatternModal />;
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return <AttitudeModal mode={LifestyleMode.CREATE} />;
    default:
      return <></>;
  }
};

const getFormTitle = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return 'Practice';
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return 'Practice';
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return 'Attitude';
    default:
      return 'Add Record';
  }
};

const FormModals = () => {
  const { source, modalOpen, setModalOpen } = lifestyleStore();

  return (
    <>
      <AppButton
        onClick={() => setModalOpen(true)}
        fullWidth
        disabled={!source}
      >
        Manually
      </AppButton>
      <AppModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title={getFormTitle(source)}
        classes={{
          root: 'w-[60vw] h-[80vh] flex flex-col min-h-0',
          body: 'flex-1 h-full flex flex-col overflow-hidden min-h-0',
        }}
      >
        {getFormModal(source)}
      </AppModal>
    </>
  );
};

export default memo(FormModals);
