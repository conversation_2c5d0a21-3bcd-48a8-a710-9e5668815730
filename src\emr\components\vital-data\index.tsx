import { useMemo } from 'react';

import { calculateVitalsAnalytics } from '@/helpers/vitals';

import VitalsCard from '@/components/vitals-card';

import { convertToVitalCard } from '@/emr/components/consultation/vitals-tab/vitals-utils';

import { PatientRecord } from '@/types';

type VitalSummaryProps = {
  vitals: PatientRecord[];
  containerClassName?: string;
};

const VitalSummary = ({
  vitals: patientVitals,
  containerClassName = '',
}: VitalSummaryProps) => {
  const vitals = patientVitals?.map(
    ({ vitals, updated_on, vitalStatuses }) => ({
      ...vitals,
      updatedOn: updated_on,
      vitalStatuses,
    })
  );

  const latestVital = vitals?.at(-1) || null;
  const analytics = calculateVitalsAnalytics(vitals as any);

  const vitalData = useMemo(
    () => [
      convertToVitalCard(
        latestVital?.height || '--',
        'cm',
        'Height',
        analytics.height,
        latestVital?.vitalStatuses
      ),
      convertToVitalCard(
        latestVital?.weight || '--',
        'kg',
        'Weight',
        analytics.weight,
        latestVital?.vitalStatuses
      ),
      convertToVitalCard(
        latestVital?.sbp && latestVital?.dbp
          ? `${latestVital?.sbp ?? ''}/${latestVital?.dbp ?? ''}`
          : '--',
        '',
        'BP',
        analytics.bloodPressure,
        latestVital?.vitalStatuses
      ),
      convertToVitalCard(
        latestVital?.bmi || '--',
        '',
        'BMI',
        analytics.bmi,
        latestVital?.vitalStatuses
      ),
      convertToVitalCard(
        latestVital?.pulse || '--',
        'BPM',
        'Pulse',
        analytics.heartRate,
        latestVital?.vitalStatuses
      ),
      convertToVitalCard(
        latestVital?.rr || '--',
        'Un',
        'RR',
        analytics.respiratoryRate,
        latestVital?.vitalStatuses
      ),
      // TODO: Uncomment when SpO2 and PP vitals are added. Currently not needed in patient info - will uncomment if needed in the future.
      // convertToVitalCard(
      //   (latestVital as any)?.spO2 || '--',
      //   '%',
      //   'SpO2',
      //   analytics.spO2,
      //   latestVital?.vitalStatuses
      // ),
      // convertToVitalCard(
      //   (latestVital as any)?.sbp && (latestVital as any)?.dbp
      //     ? (() => {
      //         const systolic = parseFloat((latestVital as any).sbp);
      //         const diastolic = parseFloat((latestVital as any).dbp);
      //         if (!isNaN(systolic) && !isNaN(diastolic)) {
      //           return (systolic - diastolic).toString();
      //         }
      //         return '--';
      //       })()
      //     : '--',
      //   'mmHg',
      //   'PP',
      //   analytics.pp,
      //   latestVital?.vitalStatuses
      // ),
    ],
    [latestVital, analytics]
  );

  return (
    <div className={`gap-1 col-span-10 flex ${containerClassName}`}>
      {vitalData.map((vitalCard, index) => (
        <VitalsCard key={index} loading={false} {...vitalCard} />
      ))}
    </div>
  );
};

export default VitalSummary;
