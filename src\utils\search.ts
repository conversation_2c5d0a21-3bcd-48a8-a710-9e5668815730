import { debounce } from 'lodash';

type SearchFunction<T> = (searchTerm: string) => Promise<T[]>;

export function createDebouncedSearch<T>(
  searchFn: SearchFunction<T>,
  wait = 300,
  options?: {
    minLength?: number;
    onStart?: () => void;
    onComplete?: () => void;
    onError?: (error: unknown) => void;
  }
) {
  const { minLength = 1, onStart, onComplete, onError } = options || {};

  const debouncedSearch = debounce(
    async (
      searchTerm: string,
      callback: (results: T[]) => void,
      signal?: AbortSignal
    ) => {
      const trimmedSearch = searchTerm.trim();

      // Skip empty or too short searches
      if (!trimmedSearch || trimmedSearch.length < minLength) {
        callback([]);
        return;
      }

      try {
        onStart?.();
        const results = await searchFn(trimmedSearch);

        // Check if the request was aborted
        if (signal?.aborted) return;

        callback(results);
      } catch (error) {
        if (signal?.aborted) return;
        onError?.(error);
        console.error('Search error:', error);
      } finally {
        onComplete?.();
      }
    },
    wait
  );

  return {
    search: (
      searchTerm: string,
      callback: (results: T[]) => void,
      signal?: AbortSignal
    ) => {
      return debouncedSearch(searchTerm, callback, signal);
    },
    cancel: () => {
      debouncedSearch.cancel();
    },
    flush: () => {
      debouncedSearch.flush();
    },
  };
}
