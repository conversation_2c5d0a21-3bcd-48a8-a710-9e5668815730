import { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { DemographicsForm } from '@/types/emr/lifestyle/demographics';

import ContactsTable from './ContactsTable';

const DemographicsView = () => {
  const { getValues } = useFormContext<DemographicsForm>();

  const demographics = getValues();

  return (
    <div className="flex flex-col h-full p-6 space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-2">
          <div className="text-sm text-gray-600">Patient name</div>
          <div className="font-medium">{demographics?.name || '-'}</div>
        </div>
        <div className="space-y-2">
          <div className="text-sm text-gray-600">CMCH ID</div>
          <div className="font-medium">{demographics?.cmchId || '-'}</div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="space-y-2">
          <div className="text-sm text-gray-600">Date of birth</div>
          <div className="font-medium">{demographics?.dob || '-'}</div>
        </div>
        <div className="space-y-2">
          <div className="text-sm text-gray-600">Age</div>
          <div className="font-medium">{demographics?.age || '-'}</div>
        </div>
        <div className="space-y-2">
          <div className="text-sm text-gray-600">Gender</div>
          <div className="font-medium capitalize">
            {demographics?.sex || '-'}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="text-sm text-gray-600">Contact Information</div>
        <ContactsTable contacts={demographics?.contacts} isViewMode={true} />
      </div>
    </div>
  );
};

export default memo(DemographicsView);
