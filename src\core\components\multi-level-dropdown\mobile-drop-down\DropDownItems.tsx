import React, { memo } from 'react';

import ButtonBase from '@mui/material/ButtonBase';
import Divider from '@mui/material/Divider';
import { AiOutlineLoading } from 'react-icons/ai';
import { FaAngleRight } from 'react-icons/fa6';

import { cn } from '@/lib/utils';

import { DropDownItemsProps } from '../types';

const DropDownItems = ({
  value,
  selected,
  hasSubOption,
  lastItem,
  onClick,
  isLoading,
}: DropDownItemsProps) => {
  return (
    <div className="w-full">
      <ButtonBase className="w-full !my-1" onClick={onClick}>
        <div
          className={cn(
            'p-2 flex justify-between w-full text-sm items-center overflow-hidden rounded-md text-left',
            { [`bg-[#A2DAF8]`]: selected }
          )}
        >
          {value}
          {hasSubOption && (
            <div className="ml-2">
              {isLoading ? (
                <AiOutlineLoading className="animate-spin" />
              ) : (
                <FaAngleRight fontSize={15} />
              )}
            </div>
          )}
        </div>
      </ButtonBase>
      <Divider className={cn({ hidden: lastItem })} />
    </div>
  );
};

export default memo(DropDownItems);
