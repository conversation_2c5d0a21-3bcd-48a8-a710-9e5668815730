'use client';

import { useEffect, useMemo, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import TextInput from '@core/components/text-input';

import {
  MultilevelDropdownType,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { getNestedValue } from '@/utils/emr/doctor-profile/personal-info';
import {
  emailPattern,
  mobileNumberPattern,
  preventNonAlphabeticInput,
  restrictMaxLength,
} from '@/utils/validation';

import PencilIcon from '@/assets/svg/PencilIcon';

import { departmentOptions } from '@/constants/emr/doctor-profile/customise-emr';
import { profileTabs } from '@/constants/emr/doctor-profile/personal-info';

import SectionTitle from '@/views/emr/doctor-profile/personal-info/SectionTitle';

import MultiLevelDropdown from '@/core/components/multi-level-dropdown';
import { GeneralInfo } from '@/types/emr/doctor-profile/personal-info';

import AutoResizeTextArea from './shared/AutoResizeTextArea';
import SaveButton from './shared/SaveButton';

export default function GeneralSection() {
  const [editableField, setEditableField] = useState<Set<string>>(new Set());
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { data: userData } = useUserStore();
  const {
    doctorProfile,
    createDoctorProfile,
    updateDoctorProfile,
    setTabName,
    fetchDoctorProfileByEmail,
  } = useDoctorStore();
  const { dropdownOptions, isLoading, fetchDropdownOptions } =
    useCustomiseEmrStore();

  const methods = useForm({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      fullName: '',
      designation: '',
      department: '',
      doctorID: '',
      contactNumber: '',
      workEmail: '',
    },
  });

  const {
    register,
    handleSubmit: handleSubmitForm,
    reset,
    formState: { errors },
    control,
  } = methods;

  useEffect(() => {
    if (doctorProfile) {
      reset({
        fullName: doctorProfile?.general?.fullName || '',
        designation: doctorProfile?.general?.designation || '',
        department: doctorProfile?.general?.department,
        doctorID: doctorProfile?.general?.doctorID || '',
        contactNumber: doctorProfile?.general?.contactNumber || '',
        workEmail: doctorProfile?.general?.workEmail || '',
      });
    }
  }, [doctorProfile, reset]);

  const onSubmit = async ({ fullName, ...data }: GeneralInfo) => {
    try {
      const payload = {
        general: { ...data, fullName: fullName ?? ' ' },
      };

      if (doctorProfile?.id) {
        await updateDoctorProfile(doctorProfile.id, payload);
        setEditableField(new Set());
        setIsSubmitted(true);
      } else {
        await createDoctorProfile({ ...payload, username: userData?.email });
        setEditableField(new Set());
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    setTabName(profileTabs.GENERAL_DETAILS);
    if (userData?.email) {
      fetchDoctorProfileByEmail(userData.email);
    }
  }, [setTabName, userData?.email, fetchDoctorProfileByEmail]);

  const handleEditClick = (fieldName: string) => {
    setEditableField((prev) => new Set(prev.add(fieldName)));
  };

  const isFieldDisabled = (fieldName: string) => {
    if (editableField.has(fieldName)) {
      return false;
    }

    const formValues = methods.getValues() as GeneralInfo;
    const fieldValue = formValues[fieldName as keyof GeneralInfo];
    const doctorFieldValue = getNestedValue(doctorProfile?.general, fieldName);

    return (
      !!doctorFieldValue ||
      (isSubmitted && !!fieldValue && fieldValue === doctorFieldValue)
    );
  };

  const renderEditIcon = (fieldName: keyof GeneralInfo) => {
    return isFieldDisabled(fieldName) ? (
      <button
        className="mt-2"
        type="button"
        onClick={() => handleEditClick(fieldName)}
      >
        <PencilIcon className="h-4 w-auto text-[#9A9A9A]" />
      </button>
    ) : null;
  };

  const getDepartmentOptions = useMemo(() => {
    return departmentOptions.map(({ key, value }) => {
      const subOptions =
        Array.isArray(dropdownOptions[key]) && dropdownOptions[key].length > 0
          ? dropdownOptions[key][0].data?.map(
              (item: MultilevelDropdownType) => ({
                key: item?.key,
                value: item?.value,
                subOptions: Array.isArray(item?.subparameters)
                  ? item?.subparameters?.map((sub) => ({
                      key: sub.key,
                      value: sub.value,
                    }))
                  : [],
              })
            ) || []
          : [];
      return {
        key,
        value,
        subOptions,
      };
    });
  }, [dropdownOptions]);

  return (
    <div className="w-full mb-3 pb-5 md:pb-0">
      <SectionTitle className="mb-5 mt-1" title="General Details" />
      <FormProvider {...methods}>
        <form onSubmit={handleSubmitForm(onSubmit)}>
          <div className="flex flex-wrap flex-col w-full gap-4">
            <div className="flex flex-nowrap flex-col md:flex-row gap-4 w-full">
              <AutoResizeTextArea
                label="Name"
                placeholder="Your Name"
                color="white"
                control={control}
                name="fullName"
                rules={{
                  required: 'Name is required',
                  validate: (value) =>
                    value?.trim().length > 0 || 'Name cannot be empty',
                }}
                onKeyDown={preventNonAlphabeticInput}
                errors={errors?.['fullName']}
                disabled={isFieldDisabled('fullName')}
                endDecoration={renderEditIcon('fullName')}
                className="flex-1"
              />
              <AutoResizeTextArea
                label="Designation"
                placeholder="Your Designation"
                color="white"
                control={control}
                name="designation"
                errors={errors?.['designation']}
                disabled={isFieldDisabled('designation')}
                endDecoration={renderEditIcon('designation')}
                className="flex-1"
              />
              <MultiLevelDropdown
                options={getDepartmentOptions}
                name="department"
                label="Department"
                placeholder="Select Your Department"
                disabledInput={isFieldDisabled('department')}
                endDecoration={renderEditIcon('department')}
                className="flex-1"
                isMultiLevel
                loading={isLoading}
                onSelect={(value, key) => {
                  if (key) {
                    fetchDropdownOptions(key);
                  }
                }}
              />
            </div>
            <div className="flex flex-nowrap flex-col md:flex-row gap-4 w-full">
              <div className="w-full md:w-2/3 flex gap-2 md:gap-4">
                <TextInput
                  label="Employee ID"
                  placeholder="552066338821"
                  color="white"
                  {...register('doctorID')}
                  errors={errors?.['doctorID']}
                  disabled={isFieldDisabled('doctorID')}
                  endDecoration={renderEditIcon('doctorID')}
                  className="flex-1"
                />
                <TextInput
                  label="Mobile"
                  placeholder="9876543210"
                  color="white"
                  {...register('contactNumber', {
                    pattern: mobileNumberPattern(),
                  })}
                  onKeyDown={restrictMaxLength(10)}
                  errors={errors?.['contactNumber']}
                  disabled={isFieldDisabled('contactNumber')}
                  endDecoration={renderEditIcon('contactNumber')}
                  className="flex-1"
                />
              </div>
              <TextInput
                label="Work Email"
                placeholder="<EMAIL>"
                color="white"
                {...register('workEmail', {
                  pattern: emailPattern(),
                })}
                errors={errors?.['workEmail']}
                disabled={isFieldDisabled('workEmail')}
                endDecoration={renderEditIcon('workEmail')}
                className="w-full md:w-1/3"
              />
            </div>
          </div>
          <SaveButton className="mt-15" />
        </form>
      </FormProvider>
    </div>
  );
}
