import React, { FC, memo } from 'react';

import { Controller, Path, useFormContext } from 'react-hook-form';

import {
  Collapse,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';

import { renderItalicText } from '@/utils/textUtil';

import { LifestyleQuestion, Questions } from '@/emr/types/lifestyle';

import RenderFields from './RenderFields';

type Props = {
  name: Path<LifestyleQuestion>;
  isReadOnly?: boolean;
  question: Questions;
};

const RenderRadio: FC<Props> = ({ name, isReadOnly, question }) => {
  const { control, watch } = useFormContext();

  const value = watch(name);

  return (
    <div>
      <Typography
        sx={{
          fontSize: { xs: '14px', sm: '16px', mt: 1 },
        }}
      >
        {renderItalicText(question?.question)}
      </Typography>
      <Controller
        control={control}
        name={`${name}.value`}
        render={({ field }) => {
          return (
            <RadioGroup row {...field}>
              {question?.options?.map((option) => (
                <FormControlLabel
                  key={option}
                  value={option}
                  disabled={isReadOnly}
                  control={
                    <Radio
                      sx={{ color: '#001926 !important' }}
                      checked={option === field.value}
                      size="small"
                    />
                  }
                  label={
                    <Typography sx={{ fontSize: '13px' }}>{option}</Typography>
                  }
                />
              ))}
            </RadioGroup>
          );
        }}
      />
      <Collapse
        in={Boolean(value?.value === 'Yes' && question?.sub_questions)}
        sx={{ mt: 1 }}
      >
        {question?.sub_questions && (
          <RenderFields
            questions={question?.sub_questions as Questions[]}
            name={`${name}.sub_questions` as Path<LifestyleQuestion>}
            control={control}
            isReadOnly={isReadOnly}
          />
        )}
      </Collapse>
    </div>
  );
};

export default memo(RenderRadio);
