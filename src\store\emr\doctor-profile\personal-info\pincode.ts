import { debounce } from 'lodash';
import { toast } from 'sonner';
import { create } from 'zustand';

import { getAddressByPincode } from '@/query/addressByPincode';

interface LocationData {
  district: string;
  state: string;
  country: string;
}

interface Street {
  Name: string;
  Block: string;
}

interface PostalStore {
  currentStreets: { value: string; label: string; city: string }[];
  permanentStreets: { value: string; label: string; city: string }[];
  locationDataByPincode: {
    current: LocationData | null;
    permanent: LocationData | null;
  };
  selectedCurrentStreet: string | null;
  selectedPermanentStreet: string | null;
  loading: boolean;
  error: string | null;
  currentPinCode: string;
  permanentPinCode: string;
  setCurrentPincode: (_pincode: string) => void;
  setPermanentPincode: (_pincode: string) => void;
  setCurrentStreet: (_street: string) => void;
  setPermanentStreet: (_street: string) => void;
  fetchLocationData: () => void;
  hasCurrentPincode?: boolean;
  hasPermanentPincode?: boolean;
  setHasCurrentPincode?: (_hasInteracted: boolean) => void;
  setHasPermanentPincode?: (_hasInteracted: boolean) => void;
}

export const usePostalStore = create<PostalStore>((set, get) => {
  const debouncedFetchLocationData = debounce(async () => {
    const {
      currentPinCode,
      permanentPinCode,
      hasCurrentPincode,
      hasPermanentPincode,
    } = get();

    const fetchData = async (
      pincode: string,
      type: 'current' | 'permanent'
    ) => {
      if (!pincode || pincode.length !== 6) {
        set((state) => ({
          ...(type === 'current'
            ? { currentStreets: [] }
            : { permanentStreets: [] }),
          locationDataByPincode: {
            ...state.locationDataByPincode,
            [type]: null,
          },
        }));
        return;
      }

      set({ loading: true, error: null });

      try {
        const response = await getAddressByPincode(pincode);
        const result = response.data;

        if (result && result[0]?.Status === 'Success') {
          const postOffices = result[0].PostOffice || [];
          const firstOffice = postOffices[0];

          set((state) => ({
            ...(type === 'current'
              ? {
                  currentStreets: postOffices.map((office: Street) => ({
                    value: office.Name,
                    label: office.Name,
                    city: office.Block,
                  })),
                  locationDataByPincode: {
                    ...state.locationDataByPincode,
                    current: {
                      district: firstOffice.District,
                      state: firstOffice.State,
                      country: firstOffice.Country,
                    },
                  },
                }
              : {
                  permanentStreets: postOffices.map((office: Street) => ({
                    value: office.Name,
                    label: office.Name,
                    city: office.Block,
                  })),
                  locationDataByPincode: {
                    ...state.locationDataByPincode,
                    permanent: {
                      district: firstOffice.District,
                      state: firstOffice.State,
                      country: firstOffice.Country,
                    },
                  },
                }),
            loading: false,
          }));
        } else {
          if (hasPermanentPincode || hasCurrentPincode) {
            toast.error('No data found for the entered pincode.');
          }
          set((state) => ({
            ...(type === 'current'
              ? { currentStreets: [] }
              : { permanentStreets: [] }),
            locationDataByPincode: {
              ...state.locationDataByPincode,
              [type]: null,
            },
            error: 'Invalid Pincode',
            loading: false,
          }));
        }
      } catch (error) {
        console.error(error);
        if (hasPermanentPincode || hasCurrentPincode) {
          toast.error('Invalid Pincode.');
        }
        set((state) => ({
          ...(type === 'current'
            ? { currentStreets: [] }
            : { permanentStreets: [] }),
          locationDataByPincode: {
            ...state.locationDataByPincode,
            [type]: null,
          },
          error: 'Failed to fetch data',
          loading: false,
        }));
      }
    };

    if (hasCurrentPincode && currentPinCode) {
      await fetchData(currentPinCode, 'current');
    }

    if (hasPermanentPincode && permanentPinCode) {
      await fetchData(permanentPinCode, 'permanent');
    }
  }, 500);

  return {
    currentStreets: [],
    permanentStreets: [],
    locationDataByPincode: {
      current: null,
      permanent: null,
    },
    selectedCurrentStreet: null,
    selectedPermanentStreet: null,
    loading: false,
    error: null,
    currentPinCode: '',
    permanentPinCode: '',
    hasPermanentPincode: false,

    setHasCurrentPincode: (hasCurrentPincode: boolean) => {
      set({ hasCurrentPincode });
    },
    setHasPermanentPincode: (hasPermanentPincode: boolean) => {
      set({ hasPermanentPincode });
    },

    setCurrentPincode: (pincode) => {
      set({ currentPinCode: pincode });
      debouncedFetchLocationData();
    },

    setPermanentPincode: (pincode) => {
      set({ permanentPinCode: pincode });
      debouncedFetchLocationData();
    },

    setCurrentStreet: (street) => {
      if (typeof street !== 'string') {
        set({ selectedCurrentStreet: null });
        return;
      }

      set((state) => {
        const selectedStreetObj = state.currentStreets.find(
          (s) => s.value === street
        );

        return {
          selectedCurrentStreet: street,
          locationDataByPincode: {
            ...state.locationDataByPincode,
            current: state.locationDataByPincode.current
              ? {
                  ...state.locationDataByPincode.current,
                  city: selectedStreetObj?.city || '',
                }
              : {
                  district: '',
                  state: '',
                  country: '',
                  city: selectedStreetObj?.city || '',
                },
          },
        };
      });
    },

    setPermanentStreet: (street) => {
      if (typeof street !== 'string') {
        set({ selectedPermanentStreet: null });
        return;
      }

      set((state) => {
        const selectedStreetObj = state.permanentStreets.find(
          (s) => s.value === street
        );

        return {
          selectedPermanentStreet: street,
          locationDataByPincode: {
            ...state.locationDataByPincode,
            permanent: state.locationDataByPincode.permanent
              ? {
                  ...state.locationDataByPincode.permanent,
                  city: selectedStreetObj?.city || '',
                }
              : {
                  district: '',
                  state: '',
                  country: '',
                  city: selectedStreetObj?.city || '',
                },
          },
        };
      });
    },

    fetchLocationData: debouncedFetchLocationData,
  };
});
