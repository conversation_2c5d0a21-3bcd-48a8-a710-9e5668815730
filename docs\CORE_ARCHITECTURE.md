# Core Architecture Documentation - ARCA EMR

## Overview

This document outlines the core architectural components that power the ARCA EMR system, including authentication, state management, routing, and data flow patterns.

## Technology Stack

### Frontend Framework

- **Next.js 14**: React framework with App Router
- **React 18**: UI library with hooks and context
- **TypeScript**: Type safety and developer experience

### UI & Styling

- **Material-UI (MUI) 6**: Component library and theming
- **Tailwind CSS**: Utility-first CSS framework
- **Emotion**: CSS-in-JS for styled components

### State Management

- **Zustand**: Lightweight state management
- **React Hook Form**: Form state management

### Authentication

- **Azure AD B2C**: Identity provider
- **MSAL React**: Microsoft Authentication Library

## Core Architecture Layers

### 1. Authentication Layer (`src/core/lib/auth/`)

**MSAL Configuration** (`msal.ts`):

```typescript
// Azure AD B2C configuration
const msalConfig = {
  auth: {
    clientId: API_CONFIG.CLIENT_ID,
    authority: `https://${tenantName}.b2clogin.com/${tenantName}.onmicrosoft.com/${signinPolicy}`,
    knownAuthorities: [`${tenantName}.b2clogin.com`],
    redirectUri: API_CONFIG.APP_BASE_URL,
  },
};
```

**Authentication Services** (`services.ts`):

- Login/logout functions
- Token management
- Account handling

**Key Features**:

- Automatic token refresh
- Silent authentication
- Account persistence
- Error handling

### 2. Provider Layer (`src/core/providers/`)

**AppProviders** - Root provider wrapper:

```tsx
<AuthProvider>
  <MuiThemeProvider>
    <QueryClientProvider client={queryClient}>
      <StageWise />
      <StatusModal />
      {children}
    </QueryClientProvider>
  </MuiThemeProvider>
</AuthProvider>
```

**Provider Hierarchy**:

1. **AuthProvider**: MSAL authentication context
2. **MuiThemeProvider**: Material-UI theme configuration
3. **QueryClientProvider**: React Query for server state
4. **Global Components**: StageWise, StatusModal, Toaster

### 3. Guard Layer (`src/core/guard/`)

**AuthGuard** - Route protection:

- Checks authentication status
- Redirects to login if unauthenticated
- Manages session idle monitoring
- Preserves redirect URLs

**PermissionGuard** - Feature access control:

- Role-based access control
- Department-specific permissions
- Feature flag integration

**DepartmentGuard** - Department-specific routing:

- EMR vs MRD module access
- Department-based navigation
- Conditional feature availability

### 4. Layout Layer (`src/core/layout/`)

**Shared Layout** (`shared/AppLayout.tsx`):

```tsx
<div className="h-screen flex flex-col">
  <Header />
  <div className="flex gap-base h-full">
    <Sidebar items={navItem} />
    <main className="flex-1">{children}</main>
  </div>
</div>
```

**Module-Specific Layouts**:

- **EMR Layout**: Medical consultation interface
- **MRD Layout**: Medical records management
- **Auth Layout**: Login and authentication pages

## State Management Architecture

### Global State (Zustand)

**User Store** (`src/store/userStore.ts`):

- User profile and permissions
- Queue management
- Appointment tracking
- Session persistence

**Key Features**:

- LocalStorage synchronization
- Cross-tab state sync
- Optimistic updates
- Error recovery

### Server State (React Query)

**Query Organization**:

```
src/query/
├── emr/           # EMR-specific queries
├── mrd/           # MRD-specific queries
├── patient.ts     # Patient data queries
├── organization.ts # Organization queries
└── medical-codes.ts # Medical code lookups
```

**Caching Strategy**:

- Patient data: 5 minutes
- Medical codes: 1 hour
- User profile: Session duration
- Queue data: Real-time updates

### Form State (React Hook Form)

**Integration Pattern**:

```tsx
const { control, handleSubmit, formState } = useForm({
  resolver: yupResolver(validationSchema),
  defaultValues: initialData,
});
```

## Data Flow Patterns

### 1. Authentication Flow

```
User Access → AuthGuard → MSAL Check → Azure AD B2C → Token Storage → API Access
```

### 2. Data Fetching Flow

```
Component → React Query → API Call → Response Cache → UI Update
```

### 3. State Update Flow

```
User Action → Form/Store Update → API Call → Optimistic Update → Server Sync
```

## API Integration

### Axios Configuration (`src/core/lib/interceptor/`)

**Request Interceptor**:

- Automatic token attachment
- Request logging
- Error handling

**Response Interceptor**:

- Token refresh on 401
- Error standardization
- Response transformation

### API Configuration (`src/core/configs/api/`)

```typescript
const API_CONFIG = {
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
  SUBSCRIPTION_KEY: process.env.NEXT_PUBLIC_SUBSCRIPTION_KEY,
  // ... other config
};
```

## Theme System (`src/core/theme/`)

### Theme Structure

```
theme/
├── colors/        # Color palettes
├── components/    # MUI component overrides
├── palette/       # Light/dark themes
├── typography/    # Font configurations
└── shadows/       # Shadow definitions
```

### Theme Usage

```tsx
const theme = getTheme({
  mode: 'light',
  primaryColor: '#1976d2',
});
```

## Navigation System

### Route Configuration

```
app/
├── (auth)/        # Authentication routes
├── (private)/     # Protected routes
│   ├── emr/       # EMR module
│   └── mrd/       # MRD module
└── select-app/    # Module selection
```

### Navigation Items (`src/core/configs/nav-items/`)

- Dynamic navigation based on permissions
- Module-specific menu items
- Breadcrumb generation

## Error Handling

### Global Error Boundary

- Catches React component errors
- Provides fallback UI
- Error reporting integration

### API Error Handling

- Standardized error responses
- User-friendly error messages
- Automatic retry logic

### Form Validation

- Yup schema validation
- Real-time field validation
- Accessibility-compliant error display

## Performance Optimizations

### Code Splitting

- Route-based splitting
- Component lazy loading
- Dynamic imports for heavy components

### Caching Strategy

- React Query for server state
- LocalStorage for user preferences
- Session storage for temporary data

### Bundle Optimization

- Tree shaking for unused code
- Image optimization with Next.js
- CSS purging with Tailwind

## Security Considerations

### Authentication Security

- Token-based authentication
- Automatic token refresh
- Secure token storage

### Data Protection

- Input sanitization
- XSS prevention
- CSRF protection

### API Security

- Request signing
- Rate limiting
- Error message sanitization

## Development Guidelines

### Component Architecture

1. Use TypeScript for all components
2. Follow single responsibility principle
3. Implement proper error boundaries
4. Include accessibility attributes

### State Management

1. Use Zustand for client state
2. Use React Query for server state
3. Avoid prop drilling
4. Implement optimistic updates

### Performance

1. Use React.memo for expensive components
2. Implement proper key props for lists
3. Lazy load heavy components
4. Optimize re-renders

## Related Documentation

- [Authentication System](./AUTHENTICATION_SYSTEM.md) - Detailed auth implementation
- [Core Components](./CORE_COMPONENTS.md) - UI component library
- [Folder Structure Guide](./FOLDER_STRUCTURE_GUIDE.md) - Project organization
