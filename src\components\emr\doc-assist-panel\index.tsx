import { useState, useEffect, useCallback } from 'react';

import { BiSearch } from 'react-icons/bi';

import Script from 'next/script';

import { useUserStore } from '@/store/userStore';

export default function DocAssistPanel() {
  const [searchQuery, setSearchQuery] = useState('');
  const [webChatStarted, setWebChatStarted] = useState(false);
  const { data: userData } = useUserStore();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement search functionality
    console.log('Search:', searchQuery);
  };

  const startChat = useCallback(async () => {
    if (webChatStarted) {
      return;
    }

    const docWindow = window as any;
    if (!docWindow.WebChat) {
      return;
    }

    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');
    const userdata = {
      user: {
        id: userData?.email || 'anonymous',
        name: userData?.name || 'User',
      },
    };
    const userId = userdata.user.id;
    const username = userdata.user.name;
    const raw = JSON.stringify(userdata);

    try {
      const res = await fetch(
        'https://emr-dev-botfw.azurewebsites.net/api/directline/token',
        { method: 'POST', headers: myHeaders, body: raw }
      );
      const { token } = await res.json();

      docWindow.WebChat.renderWebChat(
        {
          directLine: docWindow.WebChat.createDirectLine({ token }),
          userID: userId,
          username: username,
          styleOptions: {
            botAvatarInitials: 'Bot',
            userAvatarInitials: username.charAt(0).toUpperCase(),
            hideUploadButton: true,
            bubbleBackground: '#f3f4f6',
            bubbleBorderRadius: 8,
            bubbleFromUserBackground: '#3b82f6',
            bubbleFromUserBorderRadius: 8,
            primaryFont: 'system-ui, -apple-system, sans-serif',
            rootHeight: '100%',
            rootWidth: '100%',
          },
        },
        document.getElementById('lifestyle-webchat')
      );

      setWebChatStarted(true);
    } catch (error) {
      console.error('Failed to start chat:', error);
    }
  }, [webChatStarted, userData?.email, userData?.name]);

  useEffect(() => {
    // Auto-start chat when component mounts and WebChat is available
    const checkAndStartChat = () => {
      const docWindow = window as any;
      if (docWindow.WebChat && userData?.email) {
        startChat();
      }
    };

    // Check immediately
    checkAndStartChat();

    // Also check after a short delay in case the script is still loading
    const timer = setTimeout(checkAndStartChat, 1000);

    return () => clearTimeout(timer);
  }, [startChat, userData?.email]);

  return (
    <div className="h-full flex flex-col bg-white">
      <Script src="https://cdn.botframework.com/botframework-webchat/latest/webchat.js" />

      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <h3 className="text-base lg:text-lg font-semibold text-gray-900 mb-3">
          Doc Assist
        </h3>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex items-center gap-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by Name, Patient ID, Tags etc."
              className="w-full px-2 lg:px-3 py-2 pr-8 border border-gray-300 rounded-full text-xs lg:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <BiSearch className="w-3 h-3 lg:w-4 lg:h-4" />
            </button>
          </div>
        </form>
      </div>

      {/* Alerts Section */}
      <div className="p-2 lg:p-3 border-b border-gray-200 bg-gray-50 max-h-48 overflow-y-auto">
        <div className="space-y-1 lg:space-y-2">
          <div className="flex items-start gap-1 lg:gap-2 p-1 lg:p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-red-800">
                Patient has not completed prescribed lifestyle
              </div>
              <div className="text-red-600 mt-1 hidden lg:block">
                Patient should start taking the prescribed lifestyle. More than
                3 days have passed since the prescription was given.
              </div>
            </div>
          </div>

          <div className="flex items-start gap-1 lg:gap-2 p-1 lg:p-2 bg-green-50 border border-green-200 rounded text-xs">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-green-800">
                Patient has not completed prescribed lifestyle
              </div>
              <div className="text-green-600 mt-1 hidden lg:block">
                Patient should start taking the prescribed lifestyle. More than
                3 days have passed since the prescription was given.
              </div>
            </div>
          </div>

          <div className="flex items-start gap-1 lg:gap-2 p-1 lg:p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1 flex-shrink-0"></div>
            <div>
              <div className="font-medium text-red-800">
                Patient has not completed prescribed lifestyle
              </div>
              <div className="text-red-600 mt-1 hidden lg:block">
                Patient should start taking the prescribed lifestyle. More than
                3 days have passed since the prescription was given.
              </div>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex justify-center items-center gap-2 mt-2 lg:mt-3 text-xs text-gray-500">
          <button className="hover:text-gray-700">‹</button>
          <span>1</span>
          <button className="hover:text-gray-700">›</button>
        </div>
      </div>

      {/* Chat Section */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="p-2 lg:p-3 border-b border-gray-200 flex-shrink-0">
          <h4 className="font-medium text-sm lg:text-base text-gray-900">
            Assist Chat Bot
          </h4>
        </div>

        {/* WebChat Container */}
        <div className="flex-1 overflow-hidden min-h-0">
          {!webChatStarted && (
            <div className="flex items-center justify-center h-full text-gray-500 text-sm">
              <div className="text-center">
                <p>Starting AI assistant...</p>
                <div className="mt-2 flex justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              </div>
            </div>
          )}
          <div
            id="lifestyle-webchat"
            className="h-full w-full"
            style={{ display: webChatStarted ? 'block' : 'none' }}
          />
        </div>
      </div>
    </div>
  );
}
