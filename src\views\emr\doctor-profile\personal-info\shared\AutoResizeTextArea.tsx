import React, { useCallback, useEffect, useRef } from 'react';

import {
  Control,
  Controller,
  FieldValues,
  Path,
  ControllerProps,
  useWatch,
} from 'react-hook-form';

import { TextAreaInput, TextAreaProps } from '../Components';

type Props<T extends FieldValues> = TextAreaProps & {
  control: Control<T>;
  name: Path<T>;
  rules?: ControllerProps<T>['rules'];
  autoFocus?: boolean;
};

const AutoResizeTextArea = <T extends FieldValues>({
  control,
  name,
  rules,
  autoFocus = false,
  ...props
}: Props<T>) => {
  const textareaRef = useRef<HTMLTextAreaElement | null>(
    null
  ) as React.MutableRefObject<HTMLTextAreaElement | null>;

  const value = useWatch({ control, name });

  const autoResize = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
    }
  }, []);

  useEffect(() => {
    const textarea = textareaRef.current;
    textarea?.addEventListener('resize', autoResize);
    return () => {
      textarea?.removeEventListener('resize', autoResize);
    };
  }, [autoResize]);

  useEffect(() => {
    autoResize();
  }, [value, autoResize]);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field, fieldState }) => (
        <TextAreaInput
          rows={1}
          errors={fieldState?.error}
          {...field}
          {...props}
          onChange={(e) => {
            autoResize();
            field.onChange(e.target.value);
            props?.onChange?.(e);
          }}
          ref={(el) => {
            textareaRef.current = el;
            field.ref(el);
          }}
          inputClassName="min-h-[40px]"
          onInput={(e) => {
            autoResize();
            props.onInput?.(e);
          }}
          textareaClassName={'overflow-hidden !pr-10 !px-2'}
        />
      )}
    />
  );
};

export default AutoResizeTextArea;
