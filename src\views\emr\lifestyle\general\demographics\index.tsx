'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { FormProvider, useForm, useFieldArray } from 'react-hook-form';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDemographicsStore } from '@/store/emr/lifestyle/general/demographics';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { calculateAge } from '@/utils/mrd/manage-patient/calculate-age';

import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import AutoResizeTextArea from '@/views/emr/doctor-profile/personal-info/shared/AutoResizeTextArea';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledRadio from '@/components/controlled-inputs/ControlledRadio';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import {
  defaultValues,
  DemographicsForm,
} from '@/types/emr/lifestyle/demographics';

import ContactsTable from './ContactsTable';
import DemographicsView from './DemographicsView';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const Demographics = () => {
  const { patient } = useCurrentPatientStore();
  const {
    demographics,
    getPatientDemographics,
    createDemographics,
    updateDemographics,
    updating,
  } = useDemographicsStore();

  const [isViewMode, setIsViewMode] = useState(true);

  const methods = useForm<DemographicsForm>({ defaultValues });

  const { control, handleSubmit, register, reset } = methods;
  const contactFields = useFieldArray({
    control,
    name: 'contacts',
  });

  const onSubmit = useCallback(
    async (data: DemographicsForm) => {
      if (demographics) {
        await updateDemographics({
          ...data,
          id: demographics?.id,
          maritalStatus: data?.maritalStatus?.value ?? '',
        });
      } else {
        await createDemographics({
          ...data,
          dob: patient?.dob ?? '',
          maritalStatus: data?.maritalStatus?.value ?? '',
          age: calculateAge(patient?.dob),
          cmchId: patient?.id ?? '',
        });
      }
      setIsViewMode(true);
    },
    [createDemographics, demographics, patient, updateDemographics]
  );

  const handleReset = useCallback(() => {
    if (demographics) {
      reset({
        ...demographics,
        contacts: demographics?.contacts?.map((contact) => ({
          phone: contact?.phone,
          email: contact?.email,
        })),
        maritalStatus: maritalStatusOptions?.find(
          (option) => option.value === demographics?.maritalStatus
        ),
        age: calculateAge(demographics?.dob),
      });
    } else {
      reset({
        age: calculateAge(patient?.dob),
        cmchId: patient?.id,
        dob: patient?.dob,
        maritalStatus: maritalStatusOptions?.find(
          (option) => option.value === patient?.maritalStatus
        ),
        name: patient?.name,
        sex: patient?.sex,
        contacts: [
          {
            phone: patient?.contact?.phone,
            email: patient?.contact?.email,
          },
        ],
      });
    }
  }, [demographics, patient, reset]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => {
    getPatientDemographics();
  }, [getPatientDemographics]);

  return (
    <FormProvider {...methods}>
      {isViewMode ? (
        <div className="flex flex-col h-full">
          <DemographicsView />
          <div className="p-4">
            <div className="flex justify-end">
              <AppButton
                variant="outlined"
                onClick={() => setIsViewMode(false)}
                endIcon={<AppIcon icon="carbon:edit" />}
                sx={{ minWidth: 140 }}
              >
                Edit Details
              </AppButton>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col h-full">
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col flex-grow"
          >
            <div className="flex-grow p-6 space-y-6 overflow-y-auto">
              {/* First Row */}
              <div className="grid grid-cols-4 gap-6">
                <div className="col-span-1">
                  <AutoResizeTextArea
                    label="Patient name"
                    name="name"
                    control={control}
                    labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                    disabled
                  />
                </div>
                <div className="col-span-1">
                  <AutoResizeTextArea
                    label="CMCH ID"
                    name="cmchId"
                    control={control}
                    labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                    disabled
                  />
                </div>
                <div className="col-span-1">
                  <ControlledDatePicker
                    label="Date of birth"
                    name="dob"
                    control={control}
                    disabled
                  />
                </div>
                <div className="col-span-1">
                  <AutoResizeTextArea
                    label="Age"
                    name="age"
                    control={control}
                    labelClassName='className="mb-0 md:mb-0 text-sm md:text-sm'
                    disabled
                  />
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <ControlledRadio
                    label="Gender"
                    name="sex"
                    control={control}
                    options={genderOptions}
                  />
                </div>
                <div>
                  <ControlledSelectField
                    label="Marital status"
                    name="maritalStatus"
                    control={control}
                    options={maritalStatusOptions}
                    placeholder="Select marital status"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="mb-1 text-sm text-[#001926]">
                  Contact Information
                </div>
                <ContactsTable
                  contacts={methods.getValues().contacts}
                  fieldArray={contactFields}
                  register={register}
                />
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-end gap-base">
                <AppButton
                  variant="outlined"
                  onClick={() => setIsViewMode(true)}
                  sx={{ minWidth: 140 }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  type="submit"
                  sx={{ minWidth: 140 }}
                  loading={updating}
                >
                  Save Changes
                </AppButton>
              </div>
            </div>
          </form>
        </div>
      )}
    </FormProvider>
  );
};

export default memo(Demographics);
