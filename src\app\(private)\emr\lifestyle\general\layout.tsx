'use client';

import { memo } from 'react';

import { routes } from '@/constants/routes';

import PageTab from '@/views/emr/lifestyle/shared/page-tab';
import PageTitle from '@/views/emr/lifestyle/shared/PageTitle';

const tabs = [
  {
    label: 'Demographics',
    path: routes.EMR_LIFESTYLE_GENERAL,
  },
  {
    label: 'Medical History & Addiction',
    path: routes.EMR_LIFESTYLE_GENERAL_MEDICAL_HISTORY,
  },
];

const GeneralLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="h-full flex flex-col">
      {/* Page Title */}
      <PageTitle title="Consultation Timeline" hideFilterBy />

      {/* Tab Navigation */}
      <PageTab tabs={tabs} />

      {/* Content */}
      <div className="flex-1 overflow-hidden">{children}</div>
    </div>
  );
};

export default memo(GeneralLayout);
