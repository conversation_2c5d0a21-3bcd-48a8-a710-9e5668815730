import dayjs from 'dayjs';

/**
 * Returns the age from dob in a human-friendly format:
 * - If less than 1 month: X days
 * - If less than 1 year: X months
 * - If 1 year or more: X years
 * @param dob Date of birth (string or Date)
 * @param referenceDate Optional reference date for calculation (default: now)
 */
export function calculateAge(
  dob?: string | Date,
  referenceDate?: string | Date
): string {
  const now = referenceDate ? dayjs(referenceDate) : dayjs();
  const birth = dayjs(dob);
  if (!birth.isValid()) return '';
  const diffYears = now.diff(birth, 'year');
  if (diffYears >= 1) {
    return `${diffYears} year${diffYears > 1 ? 's' : ''}`;
  }
  const diffMonths = now.diff(birth, 'month');
  if (diffMonths >= 1) {
    return `${diffMonths} month${diffMonths > 1 ? 's' : ''}`;
  }
  const diffDays = now.diff(birth, 'day');
  return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
}
