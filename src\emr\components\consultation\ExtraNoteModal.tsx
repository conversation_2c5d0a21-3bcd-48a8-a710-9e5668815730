import React, { useEffect } from 'react';

import { useForm, Controller } from 'react-hook-form';

import { Box } from '@mui/material';
import { LuPencilLine } from 'react-icons/lu';

import EditableText from '@/lib/common/editable_text';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useDiagnosisStore } from '@/store/extraNoteDiagnosisStore';

import { noteModes } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { isHtmlContentEmpty } from '@/utils/textUtil';

import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import OutLinedIconButton from '../lifestyle/lifestyle-forms/shared/OutlinedIconButton';

import { StyledTypography } from './Common';

interface ExampleUsageProps {
  open: boolean;
  onClose: () => void;
  selectedTitle: string | null;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setShowCancelButton: React.Dispatch<React.SetStateAction<boolean>>;
  displayTitle?: string | null;
  showCancelButton?: boolean;
}

interface FormData {
  content: string;
}

const { EDIT, VIEW } = noteModes;

const DiagnosisNoteModal: React.FC<ExampleUsageProps> = ({
  open,
  onClose,
  selectedTitle,
  setOpen,
  showCancelButton,
  setShowCancelButton,
  displayTitle,
}) => {
  const { doctorProfile } = useDoctorStore();
  const { patient } = useCurrentPatientStore();
  const {
    addExtraNote,
    getExtraNotes,
    records,
    updateExtraNote,
    mode,
    setMode,
    selectedRecord,
    setSelectedRecord,
    loading,
  } = useDiagnosisStore();

  const { control, handleSubmit, setValue } = useForm<FormData>({
    defaultValues: { content: '' },
  });

  const filteredRecords =
    records?.records?.filter((record) => record.field === selectedTitle) || [];

  useEffect(() => {
    if (selectedRecord) {
      setValue('content', selectedRecord.content);
    }
  }, [selectedRecord, setValue]);

  const onSubmit = async (data: FormData) => {
    try {
      const existingRecords = records?.records || [];
      const isUpdating = !!selectedRecord?.record_id || !!records?.id;

      const newRecord = {
        record_id: selectedRecord?.record_id || `R${Date.now()}`,
        field: selectedTitle,
        content: data.content,
        doctor_id: doctorProfile?.id,
        timestamp: new Date().toISOString(),
        version: existingRecords.length
          ? existingRecords[existingRecords.length - 1].version + 1
          : 1,
        status: 'active',
      };

      let updatedRecords;

      if (selectedRecord) {
        updatedRecords = existingRecords.map((record) =>
          record.record_id === selectedRecord?.record_id
            ? { ...record, content: data.content }
            : record
        );
      } else {
        updatedRecords = [...existingRecords, newRecord];
      }

      if (isUpdating) {
        await updateExtraNote(records?.id as string, {
          records: updatedRecords,
        });
        setOpen(false);
        await getExtraNotes(patient?.id as string);
      } else {
        await addExtraNote(patient?.id as string, { records: updatedRecords });
        setOpen(false);
        await getExtraNotes(patient?.id as string);
      }

      setSelectedRecord(null);
    } catch (error) {
      console.error('Error submitting data:', error);
    }
  };

  const handleEdit = (record: any) => {
    setSelectedRecord(record);
    setMode(EDIT);
  };

  return (
    <CustomModal
      open={open}
      onClose={onClose}
      title={displayTitle ?? selectedTitle ?? 'Summary'}
      minHeight="25vh"
      maxHeight="25vh"
      width="40vw"
      formProps={
        mode !== VIEW ? { onSubmit: handleSubmit(onSubmit) } : undefined
      }
      content={
        <>
          {filteredRecords.length > 0 &&
            mode === VIEW &&
            filteredRecords.map((record, index) => {
              const isEmpty = isHtmlContentEmpty(record.content);

              return !isEmpty ? (
                <Box
                  key={index}
                  mb={2}
                  sx={{
                    borderBottom: '1px solid #C2CDD6',
                    padding: '8px',
                  }}
                  className="rounded-thin-scrollbar"
                >
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <StyledTypography variant="caption">
                      {formatDate(
                        record.timestamp,
                        DateFormats.READABLE_DATE_TIME
                      )}
                    </StyledTypography>

                    <Box display="flex" alignItems="center" sx={{ gap: 1 }}>
                      <OutLinedIconButton
                        onClick={() => {
                          handleEdit(record);
                          setShowCancelButton(true);
                        }}
                        sx={{ width: 25, height: 25 }}
                      >
                        <LuPencilLine color="black" size={14} />
                      </OutLinedIconButton>
                      {/* <OutLinedIconButton
                        sx={{ width: 25, height: 25 }}
                      >
                        <RiDeleteBin6Line color="black" size={14} />
                      </OutLinedIconButton> */}
                    </Box>
                  </Box>

                  <EditableText
                    key={record.record_id}
                    defaultValue={record.content}
                    editable={false}
                    bg="white"
                    className="break-words whitespace-pre-wrap w-full"
                    emptyPlaceholder={''}
                  />
                </Box>
              ) : null;
            })}

          {mode !== VIEW && (
            <>
              {selectedRecord && mode === EDIT && (
                <StyledTypography variant="caption">
                  {formatDate(
                    selectedRecord.timestamp,
                    DateFormats.READABLE_DATE_TIME
                  )}
                </StyledTypography>
              )}

              <Controller
                name="content"
                control={control}
                render={({ field }) => (
                  <EditableText
                    bg="white"
                    placeholder="Enter text"
                    defaultValue={mode === EDIT ? selectedRecord?.content : ''}
                    {...field}
                    emptyPlaceholder={''}
                  />
                )}
              />
            </>
          )}
        </>
      }
      actions={
        mode === VIEW && filteredRecords.length > 0 ? (
          <PrimaryButton
            className="capitalize h-8 text-md"
            onClick={() => {
              setMode('create');
              setSelectedRecord(null);
              setShowCancelButton(true);
            }}
          >
            Add New Note
          </PrimaryButton>
        ) : (
          <>
            {showCancelButton && (
              <PrimaryButton
                onClick={() => setMode(VIEW)}
                className="capitalize text-md h-8 !bg-[#637D92]"
              >
                Cancel
              </PrimaryButton>
            )}

            <PrimaryButton
              type="submit"
              className="capitalize text-md h-8"
              isLoading={loading}
            >
              Save Changes
            </PrimaryButton>
          </>
        )
      }
    />
  );
};

export default DiagnosisNoteModal;
