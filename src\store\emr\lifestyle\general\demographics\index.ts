import { toast } from 'sonner';
import { create } from 'zustand';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import {
  createPatientDemographics,
  getPatientDemographics,
  updatePatientDemographics,
} from '@/query/emr/lifestyle/demographics';

import { getErrorMessage } from '@/utils/error-message';

import { Demographics } from '@/types/emr/lifestyle/demographics';

type DemographicsStoreState = {
  demographics: Demographics | null;
  loading: boolean;
  updating: boolean;
};

type DemographicsStoreActions = {
  getPatientDemographics: () => void;
  createDemographics: (demographics: Omit<Demographics, 'id'>) => void;
  updateDemographics: (demographics: Demographics) => void;
};

export type DemographicsStore = DemographicsStoreState &
  DemographicsStoreActions;

const initialState: DemographicsStoreState = {
  demographics: null,
  loading: false,
  updating: false,
};

export const useDemographicsStore = create<DemographicsStore>((set) => ({
  ...initialState,
  getPatientDemographics: async () => {
    set({ loading: true });
    try {
      const patientId = useCurrentPatientStore.getState().patient?.id;
      if (!patientId) return;
      const data = await getPatientDemographics(patientId);
      set({ demographics: data });
    } catch (error) {
      console.error(getErrorMessage(error, 'Error fetching demographics'));
    } finally {
      set({ loading: false });
    }
  },
  createDemographics: async (demographics) => {
    set({ updating: true });

    try {
      const patientId = useCurrentPatientStore.getState().patient?.id;
      if (!patientId) return;
      const data = await createPatientDemographics(patientId, demographics);
      toast.success('Demographics created successfully');
      set({ demographics: data });
    } catch (error) {
      toast.error(getErrorMessage(error, 'Error creating demographics'));
    } finally {
      set({ updating: false });
    }
  },
  updateDemographics: async (demographics) => {
    set({ updating: true });
    try {
      const data = await updatePatientDemographics(
        demographics?.id,
        demographics
      );
      toast.success('Demographics updated successfully');
      set({ demographics: data });
    } catch (error) {
      toast.error(getErrorMessage(error, 'Error updating demographics'));
    } finally {
      set({ updating: false });
    }
  },
}));
