import dayjs, { Dayjs } from 'dayjs';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { filterBy, FilterByType } from '@/utils/filter-by-util';

import { sortOrder, SortOrder } from '@/core/components/table/types';

export type TestReportFilterState = {
  dateFilter: FilterByType | null;
  sortField: string;
  sortOrder: SortOrder;
  customStartDate: Dayjs | null;
  customEndDate: Dayjs | null;
  searchText: string;
  department: string | null;
};

export type TestReportFilterActions = {
  onSort: (_fieldName: string, _order: SortOrder | null) => void;
  onDateFilterChange: (
    _dateFilter: FilterByType,
    _customDate?: Dayjs[]
  ) => void;
  onSearchTextChange: (_searchText: string) => void;
  onDepartmentChange: (_department: string) => void;
  resetState: () => void;
};

export type TestReportFilterStore = TestReportFilterState &
  TestReportFilterActions;

const initialState: TestReportFilterState = {
  dateFilter: null,
  sortField: 'created_on',
  sortOrder: sortOrder.DESC,
  customStartDate: dayjs(),
  customEndDate: dayjs(),
  searchText: '',
  department: null,
};

export const useTestReportFilterStore = create<TestReportFilterStore>()(
  persist(
    (set) => ({
      ...initialState,
      onSort: (fieldName, order) => {
        if (order) {
          set({ sortField: fieldName, sortOrder: order });
        }
      },
      onDateFilterChange: (dateFilter, customDate) => {
        if (dateFilter === filterBy.CUSTOM_DATE) {
          if (customDate?.[0] && customDate?.[1]) {
            set({
              dateFilter,
              customStartDate: customDate?.[0],
              customEndDate: customDate?.[1],
            });
          }
        } else {
          set({ dateFilter, customStartDate: dayjs(), customEndDate: dayjs() });
        }
      },
      onSearchTextChange: (searchText) => {
        set({ searchText });
      },
      onDepartmentChange: (department) => {
        set({ department });
      },
      resetState: () => set(initialState),
    }),
    {
      name: 'test-report-filter',
    }
  )
);
