import { memo } from 'react';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import AppModal from '@/core/components/app-modal';
import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AccordionTitle from '../../../shared/AccordionTitle';

import KnowledgeModal from './KnowledgeModal';

type Props = {
  open: boolean;
  onClose: () => void;
  formFields: QuestionnaireResponse | null;
  onFinalize: (data: QuestionnaireResponse | null) => void;
};

const KnowledgeEditModal = memo<Props>(
  ({ open, onClose, formFields, onFinalize }) => {
    if (!open || !formFields) {
      return null;
    }

    return (
      <AppModal
        open={open}
        onClose={onClose}
        title=""
        classes={{
          root: 'w-[60vw] h-[80vh] flex flex-col min-h-0',
          body: 'flex-1 h-full flex flex-col min-h-0 !p-0',
          header: '!hidden',
          closeButton: '!hidden',
        }}
      >
        <div className="h-full flex flex-col">
          <div className="flex items-center w-full h-10 p-base bg-[#B4E5FE] rounded-base flex-shrink-0">
            <AccordionTitle
              expand={true}
              doctorName={formFields?.doctor?.name}
              finalised={formFields?.status === LifestyleRecordStatus.FINALIZED}
              onExpand={onClose}
              onFinalise={() => {
                onFinalize?.(formFields);
              }}
              open={open}
              stepper={['Knowledge']}
              designation={formFields?.doctor?.designation}
              department={formFields?.doctor?.department}
              date={formFields?.created_on}
            />
          </div>
          <div className="flex-1 h-full flex flex-col min-h-0 p-base">
            <KnowledgeModal
              patientData={formFields}
              mode={LifestyleMode.VIEW}
              onAfterSubmit={() => {
                onClose();
              }}
            />
          </div>
        </div>
      </AppModal>
    );
  }
);

KnowledgeEditModal.displayName = 'KnowledgeEditModal';

export default KnowledgeEditModal;
