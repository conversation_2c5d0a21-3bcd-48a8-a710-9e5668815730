import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

type Props = {
  data: QuestionnaireResponse;
};

const AttitudeTimelineForm: FC<Props> = ({ data }) => {
  const methods = useForm<QuestionnaireResponse>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!data?.questions?.length) return [];
    return data.questions;
  }, [data]);

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <AttitudeForm formFields={formFields} readonly />
    </FormProvider>
  );
};

export default memo(AttitudeTimelineForm);
