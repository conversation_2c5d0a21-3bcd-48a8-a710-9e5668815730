import { memo } from 'react';

import { IconButton } from '@mui/material';
import { FaAngleRight } from 'react-icons/fa6';

import Link from 'next/link';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

type TabItemProps = {
  category: {
    label: string;
    path: string;
    icon: React.ReactNode;
  };
  isActive: boolean;
};

const TabItem: React.FC<TabItemProps> = ({ category, isActive }) => {
  return (
    <Link href={category.path} prefetch={true}>
      <div
        style={{
          backgroundColor: isActive ? colors.common.navyBlue : 'transparent',
        }}
        className={cn(
          'flex items-center justify-between p-1 pl-3 rounded-lg cursor-pointer select-none shadow-custom-xs transition-all duration-200 border',
          {
            'text-white': isActive,
            'text-gray-700 hover:bg-gray-50': !isActive,
          }
        )}
      >
        <div className="flex items-center gap-2">
          <div
            className={cn(
              'flex-shrink-0',
              isActive ? 'text-white' : 'text-gray-600'
            )}
          >
            {category.icon}
          </div>
          <span className="text-sm font-medium">{category.label}</span>
        </div>
        <IconButton size="small">
          <FaAngleRight
            className={cn('text-white opacity-0 transition-all duration-300', {
              'translate-x-[-2rem]': !isActive,
              'opacity-100 translate-x-0': isActive,
            })}
          />
        </IconButton>
      </div>
    </Link>
  );
};

export default memo(TabItem);
