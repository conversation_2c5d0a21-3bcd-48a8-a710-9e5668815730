import { create } from 'zustand';

import { getFromDateAndDate } from '@/utils/emr/lifestyle';

import {
  LifestyleFilter,
  lifestyleFilterOptions,
} from '@/constants/emr/lifestyle/filter';

import { BaseOption } from '@/types';

type LifeStyleFilterState = {
  filterBy: BaseOption;
  fromDate: string;
  toDate: string;
};

type LifeStyleFilterActions = {
  setFilterBy: (filterBy: BaseOption) => void;
};

type LifeStyleFilterStore = LifeStyleFilterState & LifeStyleFilterActions;

const initialValue: LifeStyleFilterState = {
  filterBy: lifestyleFilterOptions[0],
  fromDate: getFromDateAndDate(
    lifestyleFilterOptions[0].value as LifestyleFilter
  )[0],
  toDate: getFromDateAndDate(
    lifestyleFilterOptions[0].value as LifestyleFilter
  )[1],
};

export const useLifestyleFilterStore = create<LifeStyleFilterStore>((set) => ({
  ...initialValue,
  setFilterBy: (filterBy: BaseOption) => {
    const [fromDate, toDate] = getFromDateAndDate(
      filterBy.value as LifestyleFilter
    );
    set({ filterBy, fromDate, toDate });
  },
}));
