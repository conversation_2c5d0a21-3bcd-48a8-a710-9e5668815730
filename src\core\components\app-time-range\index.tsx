import React from 'react';

import { Box } from '@mui/material';

import AppTimePicker from '@/core/components/app-time-picker';

import { AppTimePickerProps } from '../app-time-picker/types';

type TimePicker = Omit<AppTimePickerProps, 'value' | 'onChange'>;

export type AppTimeRangeProps = TimePicker & {
  from: string;
  to: string;
  onFromChange?: (value: string) => void;
  onToChange: (value: string) => void;
  disabled?: boolean;
  onChange?: (value: { from: string; to: string }) => void;
  value?: { from: string; to: string };
};

const AppTimeRange: React.FC<AppTimeRangeProps> = ({
  from,
  to,
  onFromChange,
  onToChange,
  onChange,
  disabled = false,
  value,
  ...rest
}) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <AppTimePicker
        value={from ?? value?.from ?? ''}
        onChange={(value) => {
          onFromChange && onFromChange(value);
          onChange && onChange({ from: value, to });
        }}
        disabled={disabled}
        {...rest}
      />
      <Box sx={{ color: '#6b7280', fontSize: '14px' }}>to</Box>
      <AppTimePicker
        value={to ?? value?.to ?? ''}
        onChange={(value) => {
          onToChange && onToChange(value);
          onChange && onChange({ from, to: value });
        }}
        disabled={disabled}
        {...rest}
      />
    </Box>
  );
};

export default AppTimeRange;
