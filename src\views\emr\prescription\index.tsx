import React, { ReactNode, useMemo, useEffect } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { usePrescriptionStore } from '@/store/emr/prescription';
import { useDiagnosisStore } from '@/store/extraNoteDiagnosisStore';

import { pageIds } from '@/utils/constants/docAssist';

import VitalsTab from '@/emr/components/consultation/vitals-tab';
import DocAssist from '@/emr/components/doc_assist';
import PatientCard from '@/emr/components/patient-card';

import {
  prescriptionTabs,
  PrescriptionTabType,
} from '@/types/emr/prescription';

import PrescriptionHistory from './history';
import NewPrescription from './NewPrescription';
import PrescriptionNotes from './PrescriptionNotes';
import PrescriptionTabSection from './PrescriptionTabSection';

const { NEW_PRESCRIPTION, PRESCRIPTION_HISTORY } = prescriptionTabs;

const Prescriptions = () => {
  const { patient } = useCurrentPatientStore();
  const { activeTab } = usePrescriptionStore();

  const { getExtraNotes } = useDiagnosisStore();

  useEffect(() => {
    if (patient?.id) {
      getExtraNotes(patient.id);
    }
  }, [patient?.id, getExtraNotes]);

  const prescriptionTabs = useMemo<Record<PrescriptionTabType, ReactNode>>(
    () => ({
      [NEW_PRESCRIPTION]: <NewPrescription />,
      [PRESCRIPTION_HISTORY]: <PrescriptionHistory />,
    }),
    []
  );

  return (
    <div className="flex w-full h-full gap-base max-w-full">
      <div className="w-75 flex-shrink-0 flex-grow-0 bg-white rounded-base h-full flex flex-col shadow-base overflow-hidden">
        <div className="p-2 border-b border-gray-200 shadow-md rounded-md min-w-0 overflow-hidden">
          <h2 className="text-sm font-semibold text-gray-700">
            Now Consulting
          </h2>
          <div>
            <div>
              <PatientCard
                name={patient?.name ?? '--'}
                dob={patient?.dob}
                sex={patient?.sex ?? '--'}
                address={patient?.address}
              />
            </div>
            <div className="mb-2">
              <VitalsTab />
            </div>
          </div>
        </div>
        <PrescriptionNotes />
      </div>
      <div
        className={`bg-white overflow-hidden pr-2 rounded-lg flex-1 min-w-0 relative`}
      >
        <PrescriptionTabSection />
        <div key={activeTab} className="min-w-0 overflow-hidden mt-4 ">
          {prescriptionTabs[activeTab]}
        </div>
      </div>
      <div
        className={`h-full bg-white rounded-base border border-[#C2CDD6] w-72 flex-shrink-0 flex-grow-0 min-w-0 overflow-auto`}
      >
        <DocAssist pageId={pageIds.PRESCRIPTION} />
      </div>
    </div>
  );
};

export default Prescriptions;
