import React from 'react';

import {
  FormControl,
  FormControlProps,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  SxProps,
} from '@mui/material';
import { MdKeyboardArrowDown } from 'react-icons/md';

export type OptionType = {
  key: string;
  value: string;
};

type CommonSelectDropdownProps = {
  label?: string;
  name: string;
  value: string;
  placeholder?: string;
  options: OptionType[];
  onChange: (value: string) => void;
  size?: 'small' | 'medium';
  className?: string;
  formControlProps?: FormControlProps;
  sx?: SxProps;
  onClickMenu?: (value: OptionType) => void;
};

const CommonSelectDropdown: React.FC<CommonSelectDropdownProps> = ({
  label = '',
  name,
  value,
  placeholder = '',
  options,
  onChange,
  size = 'small',
  className = '',
  formControlProps = {},
  sx = {},
  onClickMenu,
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    onChange(event.target.value);
  };

  const { fullWidth, ...restFormControlProps } = formControlProps;

  return (
    <FormControl
      fullWidth={fullWidth}
      size={size}
      className={className}
      {...restFormControlProps}
      sx={{
        maxWidth: 250,
        '& .MuiOutlinedInput-root': {
          fontSize: '12px',
          paddingRight: '30px',
          height: '31px',
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#ccc',
            borderWidth: '1px',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#999',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#ccc',
            borderWidth: '1px',
          },
          '&.Mui-focused': {
            boxShadow: 'none',
          },
        },
        '& .MuiSelect-select': {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        '& .MuiInputLabel-root': {
          fontSize: '12px',
        },
      }}
    >
      {label && (
        <InputLabel id={`${name}-label`} className="text-xs">
          {label}
        </InputLabel>
      )}
      <Select
        labelId={`${name}-label`}
        id={`${name}-select`}
        value={value}
        onChange={handleChange}
        displayEmpty
        size={size}
        IconComponent={MdKeyboardArrowDown}
        renderValue={(selected) => {
          const selectedOption = options.find((o) => o.key === selected);
          return selectedOption
            ? selectedOption.value
            : placeholder || 'Select an option';
        }}
        sx={sx}
      >
        {options.map((option) => (
          <MenuItem
            key={option.key}
            value={option.key}
            sx={{ fontSize: '12px' }}
            onClick={() => onClickMenu?.(option)}
          >
            {option.value}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default CommonSelectDropdown;
