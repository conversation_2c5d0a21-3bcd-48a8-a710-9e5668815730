import React from 'react';

import { Modal, Box, Typography, BoxProps } from '@mui/material';
import { AiOutlineClose } from 'react-icons/ai';

import OutLinedIconButton from '@/emr/components/lifestyle/lifestyle-forms/shared/OutlinedIconButton';

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app modal component instead.
 */
interface ReusableModalProps {
  open: boolean;
  onClose: () => void;
  title?: string | React.JSX.Element;
  content: React.ReactNode;
  actions?: React.ReactNode;
  width?: BoxProps['width'];
  minHeight?: BoxProps['minHeight'];
  maxHeight?: BoxProps['maxHeight'];
  formProps?: React.FormHTMLAttributes<HTMLFormElement>;
  showDivider?: boolean;
  actionsSx?: BoxProps['sx'];
  titleBoxSx?: BoxProps['sx'];
  titleTypographySx?: BoxProps['sx'];
  contentSx?: BoxProps['sx'];
}

/**
 * @deprecated This component is deprecated and will be removed in future versions.
 * Please use the app modal component instead.
 */
const CustomModal: React.FC<ReusableModalProps> = ({
  open,
  onClose,
  title,
  content,
  actions,
  width = '500px',
  minHeight = '200px',
  maxHeight = '80vh',
  formProps,
  showDivider = true,
  actionsSx,
  titleBoxSx,
  titleTypographySx,
  contentSx,
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-title"
      sx={{ zIndex: 10000 }}
    >
      <Box
        component="form"
        {...formProps}
        sx={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width,
          bgcolor: 'background.paper',
          boxShadow: 24,
          px: 3,
          py: 2,
          borderRadius: 2,
        }}
      >
        <Box
          sx={{
            position: 'relative',
            textAlign: 'start',
            mb: 2,
            borderBottom: showDivider ? '1px solid #ddd' : undefined,
            ...(titleBoxSx || {}),
          }}
        >
          {title && (
            <>
              <Typography
                id="modal-title"
                variant="h6"
                sx={{
                  fontWeight: 500,
                  fontSize: '20px',
                  ...(titleTypographySx || {}),
                }}
              >
                {title}
              </Typography>

              <OutLinedIconButton
                onClick={onClose}
                showBorder={false}
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: 22,
                  height: 22,
                }}
              >
                <AiOutlineClose size={16} color="black" />
              </OutLinedIconButton>
            </>
          )}
        </Box>

        <Box
          sx={{
            minHeight,
            maxHeight,
            overflowY: 'auto',
            mb: 2,
            overflowX: 'hidden',
            ...(contentSx || {}),
          }}
        >
          {content}
        </Box>

        {actions && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: 1,
              mr: { xs: 0, md: 2 },
              ...(actionsSx || {}),
            }}
          >
            {actions}
          </Box>
        )}
      </Box>
    </Modal>
  );
};

export default CustomModal;
