import dayjs from 'dayjs';
import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createNewTest,
  getLabDepartments,
  LabTestPayload,
  patientLabeTest,
  PatientLabTestParams,
  patientRecentTestResults,
  searchTests as searchTestsApi,
  uploadTestResult,
} from '@/query/emr/lab';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import {
  defaultLabTestRow,
  LabTestItem,
  LabTestTabType,
  testReportTabs,
  TestResultItem,
} from '@/types/emr/lab';

import { useCurrentPatientStore } from '../../currentPatientStore';
import { useUserStore } from '../../userStore';

interface TestStoreState {
  testSearchResults: LabTestItem[];
  selectedSearchTests: LabTestItem[];
  isLoading: boolean;
  activeTab: LabTestTabType;
  activePackage: 'dept' | 'user' | null;
  labDepartments: { key: string; value: string }[];
  selectedDepartment: string | null;
  testResult: TestResultItem[];
  recentTest: TestResultItem[];
  recentTestLoading: boolean;

  searchTests: (
    _searchTerm: string,
    _selectedDepartment: string
  ) => Promise<LabTestItem[]>;
  setSelectedSearchTest: (_test: LabTestItem, _pageKey: string) => void;
  removeSelectedSearchTest: (_id: string) => void;
  createLabTest: (_data: LabTestPayload) => Promise<LabTestItem | null>;
  clearSelectedLabTest: () => void;
  setActiveTab: (
    _tab: LabTestTabType,
    _selectedSearchTests?: LabTestItem[]
  ) => void;
  switchActiveTab: (_tab: LabTestTabType) => void;
  setActivePackage: (_value: 'dept' | 'user' | null) => void;
  fetchLabDepartments: () => Promise<void>;
  setSelectedDepartment: (_dept: string) => void;
  clearPackageTests: () => void;
  fetchPatientTestResult: (
    _params: PatientLabTestParams
  ) => Promise<TestResultItem[]>;
  uploadFile: (_data: FormData) => Promise<void>;
  fetchRecentTest: () => Promise<TestResultItem[]>;
}

const { TEST_RESULTS } = testReportTabs;
const { DATE_YYYY_MM_DD } = DateFormats;

export const useTestStore = create<TestStoreState>((set, get) => ({
  testSearchResults: [],
  selectedSearchTests: [],
  isLoading: false,
  activeTab: TEST_RESULTS,
  activePackage: null,
  labDepartments: [],
  selectedDepartment: 'All',
  testResult: [],
  recentTest: [],
  recentTestLoading: false,

  setActiveTab: (tab) => set({ activeTab: tab }),

  setActivePackage: (value) => set({ activePackage: value }),

  switchActiveTab: (tab: LabTestTabType) => {
    set({
      activeTab: tab,
      selectedSearchTests: [defaultLabTestRow],
    });
  },

  searchTests: async (searchTerm: string, selectedDepartment: string) => {
    set({ isLoading: true });
    try {
      const organizationId = useUserStore.getState().data.organizationId;
      const response = await searchTestsApi(
        searchTerm,
        selectedDepartment,
        organizationId
      );
      const results: LabTestItem[] = response.data.items || [];
      set({ testSearchResults: results, isLoading: false });
      return results;
    } catch (error) {
      console.error('Error searching tests:', error);
      toast.error('Failed to search tests');
      set({ testSearchResults: [], isLoading: false });
      return [];
    }
  },

  setSelectedSearchTest: (test: LabTestItem, pageKey?: string) => {
    const testWithPage = { ...test, pageKey };
    const existing = get().selectedSearchTests;

    const alreadyExists = existing.some((t) => t.id === test.id);

    if (alreadyExists) {
      toast.error('This test has already been added');
      return;
    }
    set((state) => ({
      selectedSearchTests: [...state.selectedSearchTests, testWithPage],
    }));
  },

  removeSelectedSearchTest: (id: string) => {
    set((state) => ({
      selectedSearchTests: state.selectedSearchTests.filter((t) => t.id !== id),
    }));
  },

  clearSelectedLabTest: () => {
    set({ selectedSearchTests: [] });
  },

  clearPackageTests: () => {
    set((state) => ({
      selectedSearchTests: state.selectedSearchTests.filter(
        (test) =>
          !test.pageKey ||
          (test.pageKey !== 'department' && test.pageKey !== 'user')
      ),
    }));
  },

  createLabTest: async (data: LabTestPayload) => {
    set({ isLoading: true });

    try {
      const response = await createNewTest(data);
      const newLabTest: LabTestItem = response.data[0]?.data || null;

      set({
        isLoading: false,
      });

      toast.success('Test ordered successfully');
      return newLabTest;
    } catch (error) {
      console.error('Failed to order test:', error);
      set({ isLoading: false });
      toast.error('Failed to order test.');
      return null;
    }
  },

  fetchLabDepartments: async () => {
    try {
      const resp = await getLabDepartments();
      const list = resp.data as string[];
      const options = list.map((dept) => ({
        key: dept,
        value: dept,
      }));
      set({
        labDepartments: [
          { value: 'All', key: 'ALL' },
          ...options,
          { value: 'Others', key: 'OTHERS' },
        ],
      });
    } catch (err) {
      console.error(err);
      toast.error('Could not load lab departments');
    }
  },

  setSelectedDepartment: (dept) => {
    return set({ selectedDepartment: dept });
  },

  fetchPatientTestResult: async (params: PatientLabTestParams) => {
    set({ isLoading: true });
    try {
      const response = await patientLabeTest(params);
      const results: TestResultItem[] = response.data || [];
      set({ testResult: results, isLoading: false });
      return results;
    } catch (error) {
      console.error('Error fetching test results:', error);
      toast.error('Failed to fetch test results');
      set({ testResult: [], isLoading: false });
      return [];
    }
  },

  uploadFile: async (data) => {
    set({ isLoading: true });
    try {
      await uploadTestResult(data);
      set({ isLoading: false });
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
      set({ isLoading: false });
    }
  },

  fetchRecentTest: async () => {
    set({ recentTestLoading: true });
    const patient = useCurrentPatientStore.getState().patient;
    if (!patient) {
      set({ recentTestLoading: false });
      return [];
    }
    try {
      const response = await patientRecentTestResults({
        patientId: patient?.id ?? '',
        startDate: formatDate(dayjs().subtract(1, 'week'), DATE_YYYY_MM_DD),
        endDate: formatDate(dayjs().add(1, 'day'), DATE_YYYY_MM_DD),
      });
      const results: TestResultItem[] = response.data || [];
      set({ recentTest: results, recentTestLoading: false });
      return results;
    } catch (error) {
      console.error('Error fetching recent tests:', error);
      set({ recentTest: [], recentTestLoading: false });
      return [];
    }
  },
}));
