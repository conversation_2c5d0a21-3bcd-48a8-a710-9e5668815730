import { create } from 'zustand';

import {
  sendAbhaOtp,
  verifyAbhaOtp,
  sendAadhaarOtp,
  verifyAadhaarOtp,
  sendAbhaOtpByMobile,
  verifyAbhaOtpByMobile,
  AbhaOtpResponse,
  VerifyOtpRequest,
  AadhaarOtpResponse,
} from '@/query/mrd/manage-patient/manage/abha';

interface AbhaStoreState {
  // ABHA OTP State
  abhaOtpRequest: AbhaOtpResponse | null;
  isSendingAbhaOtp: boolean;
  isVerifyingAbhaOtp: boolean;

  // Aadhaar OTP State
  aadhaarOtpRequest: AadhaarOtpResponse | null;
  isSendingAadhaarOtp: boolean;
  isVerifyingAadhaarOtp: boolean;
  error: string | null;

  // Actions
  sendAbhaOtp: (abhaNumber: string) => Promise<void>;
  verifyAbhaOtp: (params: VerifyOtpRequest) => Promise<AbhaOtpResponse>;
  sendAadhaarOtp: (
    aadhaar: string,
    mobile: string
  ) => Promise<AadhaarOtpResponse>;
  verifyAadhaarOtp: (
    aadhaar: string,
    otp: string,
    txnId: string
  ) => Promise<any>;
  sendAbhaOtpByMobile: (mobile: string) => Promise<AbhaOtpResponse>;
  verifyAbhaOtpByMobile: (
    otp: string,
    txnId: string
  ) => Promise<AbhaOtpResponse>;
  resetAbhaState: () => void;
}

export const useAbhaStore = create<AbhaStoreState>((set, get) => ({
  // Initial State
  abhaOtpRequest: null,
  isSendingAbhaOtp: false,
  isVerifyingAbhaOtp: false,
  aadhaarOtpRequest: null,
  isSendingAadhaarOtp: false,
  isVerifyingAadhaarOtp: false,
  error: null,

  // Send OTP to the registered mobile number for ABHA verification
  sendAbhaOtp: async (abhaNumber: string) => {
    try {
      set({ isSendingAbhaOtp: true, error: null });
      const response = await sendAbhaOtp(abhaNumber);
      set({ abhaOtpRequest: response });
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to send ABHA OTP',
      });
      throw error;
    } finally {
      set({ isSendingAbhaOtp: false });
    }
  },

  // Verify the ABHA OTP and fetch patient details
  verifyAbhaOtp: async (params: VerifyOtpRequest) => {
    try {
      set({ isVerifyingAbhaOtp: true, error: null });
      const result = await verifyAbhaOtp(params);
      return result;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to verify ABHA OTP',
      });
      throw error;
    } finally {
      set({ isVerifyingAbhaOtp: false });
    }
  },

  // Reset the ABHA store state
  // Send Aadhaar OTP
  sendAadhaarOtp: async (aadhaar: string, mobile: string) => {
    try {
      set({ isSendingAadhaarOtp: true, error: null });
      const response = await sendAadhaarOtp(aadhaar, mobile);
      set({ aadhaarOtpRequest: response });
      return response;
    } catch (error) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to send Aadhaar OTP',
      });
      throw error;
    } finally {
      set({ isSendingAadhaarOtp: false });
    }
  },

  // Verify Aadhaar OTP
  verifyAadhaarOtp: async (aadhaar: string, otp: string, txnId: string) => {
    try {
      set({ isVerifyingAadhaarOtp: true, error: null });
      const result = await verifyAadhaarOtp(aadhaar, otp, txnId);
      return result;
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to verify Aadhaar OTP',
      });
      throw error;
    } finally {
      set({ isVerifyingAadhaarOtp: false });
    }
  },

  // Send ABHA OTP by mobile
  sendAbhaOtpByMobile: async (mobile: string) => {
    try {
      set({ isSendingAbhaOtp: true, error: null });
      const response = await sendAbhaOtpByMobile(mobile);
      set({ abhaOtpRequest: response });
      return response;
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to send ABHA OTP by mobile',
      });
      throw error;
    } finally {
      set({ isSendingAbhaOtp: false });
    }
  },

  // Verify ABHA OTP by mobile
  verifyAbhaOtpByMobile: async (otp: string, txnId: string) => {
    try {
      set({ isVerifyingAbhaOtp: true, error: null });
      const result = await verifyAbhaOtpByMobile(otp, txnId);
      return result;
    } catch (error) {
      set({
        error:
          error instanceof Error
            ? error.message
            : 'Failed to verify ABHA OTP by mobile',
      });
      throw error;
    } finally {
      set({ isVerifyingAbhaOtp: false });
    }
  },

  resetAbhaState: () => {
    set({
      abhaOtpRequest: null,
      aadhaarOtpRequest: null,
      isSendingAbhaOtp: false,
      isVerifyingAbhaOtp: false,
      isSendingAadhaarOtp: false,
      isVerifyingAadhaarOtp: false,
      error: null,
    });
  },
}));

// Export a hook to use the ABHA store
export const useAbha = () => {
  const {
    abhaOtpRequest,
    isSendingAbhaOtp,
    isVerifyingAbhaOtp,
    aadhaarOtpRequest,
    isSendingAadhaarOtp,
    isVerifyingAadhaarOtp,
    error,
    sendAbhaOtp,
    verifyAbhaOtp,
    sendAadhaarOtp,
    verifyAadhaarOtp,
    sendAbhaOtpByMobile,
    verifyAbhaOtpByMobile,
    resetAbhaState,
  } = useAbhaStore();

  return {
    // State
    abhaOtpRequest,
    isSendingAbhaOtp,
    isVerifyingAbhaOtp,
    aadhaarOtpRequest,
    isSendingAadhaarOtp,
    isVerifyingAadhaarOtp,
    error,
    isAbhaOtpSent: !!abhaOtpRequest,

    // Actions
    sendAbhaOtp,
    verifyAbhaOtp,
    sendAadhaarOtp,
    verifyAadhaarOtp,
    sendAbhaOtpByMobile,
    verifyAbhaOtpByMobile,
    resetAbhaState,
  };
};
