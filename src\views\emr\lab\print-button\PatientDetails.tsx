import React, { memo, useMemo } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLabPrintStore } from '@/store/emr/lab/print-store';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { calculateAge } from '@/utils/mrd/manage-patient/calculate-age';

const PatientDetails = () => {
  const { patient } = useCurrentPatientStore();
  const { printItem, printOpen } = useLabPrintStore();

  const patientDetails = useMemo(
    () => [
      {
        label: 'Patient Name',
        value: `${patient?.name} (${patient?.sex?.slice(0, 1)})`,
      },
      {
        label: 'Patient ID',
        value: patient?.id,
      },
      { label: 'Age', value: calculateAge(patient?.dob) },
      { label: 'Mobile', value: patient?.contact?.phone },
      {
        label: 'Date',
        value: formatDate(
          printItem?.created_on,
          DateFormats.DATE_DD_MM_YYYY_SLASH
        ),
      },
    ],
    [
      patient?.contact?.phone,
      patient?.dob,
      patient?.id,
      patient?.name,
      patient?.sex,
      printItem?.created_on,
    ]
  );

  return (
    <div className="flex w-full gap-base justify-between pb-base">
      {patientDetails.map((detail) => (
        <KeyValuePair
          key={detail.label}
          variant={printOpen ? 'vertical' : 'horizontal'}
          {...detail}
        />
      ))}
    </div>
  );
};

export default memo(PatientDetails);

type KeyValuePairProps = {
  label: string;
  value?: string;
  variant?: 'vertical' | 'horizontal';
};

const KeyValuePair: React.FC<KeyValuePairProps> = ({
  label,
  value,
  variant,
}) => {
  return (
    <div
      className={
        variant === 'vertical'
          ? 'flex flex-col w-fit flex-nowrap'
          : 'flex w-fit flex-nowrap'
      }
    >
      <div className="font-bold whitespace-nowrap text-sm text-gray-600">
        {label}:
      </div>
      {variant === 'horizontal' && <div className="w-1" />}
      <div className="font-bold whitespace-nowrap text-sm">{value}</div>
    </div>
  );
};
