import { FC, memo } from 'react';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
  showHeading?: boolean;
};

const AttitudeForm: FC<Props> = ({
  formFields,
  readonly,
  showHeading = true,
}) => {
  const renderIcon = (icon: string) => {
    if (icon === 'attitude') {
      return null;
    }
    return <span className="text-2xl">{icon}</span>;
  };

  return (
    <div className={`space-y-6 p-4 ${readonly ? 'pointer-events-none' : ''}`}>
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6">
          {showHeading && section.icon !== 'attitude' && (
            <div className="flex items-center space-x-2">
              {section.icon && renderIcon(section.icon)}
              <h3 className="text-lg font-medium">{section.title}</h3>
            </div>
          )}
          <div>
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(AttitudeForm);
