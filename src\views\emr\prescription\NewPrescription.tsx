import React, { useState, useEffect, useMemo, useCallback } from 'react';

import { useForm, useFieldArray } from 'react-hook-form';

import { Box } from '@mui/material';

import { cn } from '@/lib/utils';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePrescriptionStore } from '@/store/emr/prescription';
import { useUserStore } from '@/store/userStore';

import { getDoctorName } from '@/utils/constants/lifeStyle';

import FieldValidationError from '@/emr/components/shared/FieldValidationError';

import PrimaryButton from '@/core/components/primary-button';
import {
  defaultPrescriptionRow,
  PrescriptionItem,
  PrescriptionModalType,
  prescriptionModalTypes,
  prescriptionMode,
  prescriptionTabs,
} from '@/types/emr/prescription';

import PrescriptionTable from './PrescriptionTable';

import { ClearConfirmationModal } from './shared/ClearModal';
import ConfirmationModal from './shared/SaveConfirmationModal';

export type Prescription = {
  prescription: PrescriptionItem[];
};

type Props = {
  tableHeight?: string;
};

const { PRESCRIPTION_HISTORY } = prescriptionTabs;
const { SAVE, CLEAR } = prescriptionModalTypes;
const { VIEW, NEW } = prescriptionMode;

const NewPrescription: React.FC<Props> = () => {
  const {
    selectedMedicines,
    clearSelectedMedicines,
    removeSelectedMedicine,
    setActiveTab,
    createPrescription,
    updatePrescription,
    setSelectedMedicines,
    onExpand,
    prescriptionMode: mode,
    selectedHistory: prescription,
    isLoading,
  } = usePrescriptionStore();
  const { name } = useUserStore();
  const { patient } = useCurrentPatientStore();
  const doctor = useDoctorStore((state) => state.doctorProfile);
  const { data: userData } = useUserStore();

  const [modalType, setModalType] = useState<PrescriptionModalType | null>(
    null
  );

  const defaultValues = useMemo(
    () => ({
      prescription:
        mode !== NEW ? prescription?.medicines : [defaultPrescriptionRow],
    }),
    [mode, prescription?.medicines]
  );

  const {
    control,
    handleSubmit,
    clearErrors,
    reset,
    trigger,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<Prescription>({
    defaultValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const { fields, remove, update } = useFieldArray({
    control,
    name: 'prescription',
  });

  const watchPrescription = watch('prescription');

  const totalCost = useMemo(() => {
    if (!watchPrescription || watchPrescription.length === 0) return '00.00';

    const total = watchPrescription.reduce((sum, item) => {
      const cost = parseFloat(item.cost || '0');

      return sum + (isNaN(cost) ? 0 : cost);
    }, 0);

    return total.toFixed(2);
  }, [watchPrescription]);

  const isReadOnly = useMemo(() => mode === VIEW, [mode]);

  const handleRemove = useCallback(
    (index: number, id: string) => {
      if (fields.length === 1) {
        update(index, defaultPrescriptionRow);
      } else {
        remove(index);
      }
      removeSelectedMedicine(id);
    },
    [fields.length, remove, removeSelectedMedicine, update]
  );

  const onSubmit = useCallback(
    async (data: Prescription) => {
      const doctorName = getDoctorName(doctor?.general?.fullName, name);
      const payload = {
        patientId: patient?.id,
        doctor: doctorName,
        medicines: data.prescription ?? [],
        doctorEmail: userData.email,
      };

      if (prescription?.id) {
        return await updatePrescription(prescription.id, payload);
      } else {
        const response = await createPrescription(payload);
        reset({ prescription: [defaultPrescriptionRow] });
        clearSelectedMedicines();
        return response;
      }
    },
    [
      doctor?.general?.fullName,
      name,
      patient?.id,
      userData?.email,
      prescription?.id,
      updatePrescription,
      createPrescription,
      reset,
      clearSelectedMedicines,
    ]
  );

  const handleSaveClick = useCallback(async () => {
    const isValid = await trigger('prescription');
    if (!isValid) {
      setValue('prescription', [...watchPrescription], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    } else {
      setModalType(SAVE);
    }
  }, [trigger, setValue, watchPrescription]);

  const handleClearClick = useCallback(() => {
    setModalType(CLEAR);
  }, []);

  const handleSaveConfirm = useCallback(() => {
    const currentPrescriptions = getValues('prescription');
    const isDefaultPrescriptionOnly =
      currentPrescriptions.length === 1 &&
      Object.keys(defaultPrescriptionRow).every(
        (key) => currentPrescriptions[0][key] === defaultPrescriptionRow[key]
      );

    if (!isDefaultPrescriptionOnly) {
      handleSubmit(async (data) => {
        const response = await onSubmit(data);
        setModalType(null);
        setActiveTab(PRESCRIPTION_HISTORY);
        response && onExpand(response);
      })();
    } else {
      setModalType(null);
    }
  }, [getValues, handleSubmit, onSubmit, setActiveTab, onExpand]);

  const handleClearConfirm = useCallback(() => {
    reset({ prescription: [defaultPrescriptionRow] });
    setModalType(null);
    clearSelectedMedicines();
  }, [reset, clearSelectedMedicines]);

  const handleCancel = useCallback(() => {
    setModalType(null);
  }, []);

  useEffect(() => {
    if (mode === NEW) {
      setSelectedMedicines([]);
      return;
    }

    if (!prescription?.medicines) return;

    const itemsForStore = prescription.medicines.map((item) => {
      const numQuantity = parseFloat(String(item.quantity));
      const numTotalCost = parseFloat(String(item.cost));
      const unitCost =
        numQuantity > 0 && !isNaN(numTotalCost) && isFinite(numQuantity)
          ? (numTotalCost / numQuantity).toFixed(2)
          : '0';

      return { ...item, Cost: unitCost };
    });

    setSelectedMedicines(itemsForStore);
  }, [prescription?.medicines, setSelectedMedicines, mode]);

  useEffect(() => {
    if (selectedMedicines.length > 0) {
      const currentPrescription = getValues('prescription');

      const formattedMedicines = selectedMedicines.map((medicine) => {
        const isMedicineAlreadyInPrescription = currentPrescription.find(
          (item) => item.id === medicine.id
        );

        if (isMedicineAlreadyInPrescription) {
          return isMedicineAlreadyInPrescription;
        }
        const quantity = parseFloat(medicine.quantity ?? '1');
        const costPerUnit = parseFloat(String(medicine.Cost || '0'));
        const totalCost =
          !isNaN(quantity) && !isNaN(costPerUnit)
            ? (quantity * costPerUnit).toFixed(2)
            : '0';

        return {
          ...medicine,
          drugForm: medicine.DrugFormulation,
          genericName: medicine.GenericName,
          brandName: medicine.BrandName,
          strength: medicine.Strength,
          measure: medicine.Measure,
          uom: medicine.UnitOfMeasure,
          quantity: medicine.quantity ?? '1',
          cost: totalCost,
        };
      });

      reset({ prescription: formattedMedicines });
    }
  }, [selectedMedicines, reset, getValues]);

  useEffect(() => {
    if (mode === VIEW && prescription?.medicines) {
      reset(
        { prescription: prescription.medicines },
        { keepDefaultValues: false }
      );
    }
  }, [prescription?.medicines, mode, reset]);

  useEffect(() => {
    const handleFieldChange = (name: string, value: any) => {
      if (!name) return;

      // Handle quantity changes
      if (name.includes('.quantity')) {
        const match = name.match(/prescription\.(\d+)\.quantity/);
        if (match) {
          const index = parseInt(match[1]);
          const medicineCost = selectedMedicines[index]?.Cost;
          const quantity = parseFloat(value);

          if (!value) {
            setValue(`prescription.${index}.cost`, '', {
              shouldValidate: true,
            });
            clearErrors(`prescription.${index}.cost`);
          } else if (!isNaN(quantity) && medicineCost) {
            const costPerUnit = parseFloat(medicineCost as string);
            if (!isNaN(costPerUnit)) {
              const totalCost = (quantity * costPerUnit).toFixed(2);
              setValue(`prescription.${index}.cost`, totalCost, {
                shouldValidate: true,
              });
              clearErrors(`prescription.${index}.cost`);
            }
          }
          return;
        }
      }

      // Handle other field changes (instruction, duration, frequency, route)
      const fieldMatch = name.match(
        /^prescription\.(\d+)\.(instruction|duration|frequency|route)$/
      );
      if (fieldMatch) {
        const index = parseInt(fieldMatch[1]);
        const field = fieldMatch[2];

        if (value !== undefined && value !== null && value !== '') {
          // Use setTimeout to defer the validation to the next tick
          setTimeout(() => {
            trigger(`prescription.${index}.${field}`);
          }, 0);
        }
      }
    };

    const subscription = watch((value, { name, type }) => {
      if (type === 'change' && name) {
        handleFieldChange(name, getValues(name));
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, trigger, getValues, setValue, selectedMedicines, clearErrors]);

  useEffect(() => {
    return () => {
      reset({ prescription: [defaultPrescriptionRow] });
    };
  }, [reset]);

  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name?.startsWith('prescription.')) {
        const fieldValue = getValues(name);
        if (
          fieldValue !== undefined &&
          fieldValue !== null &&
          fieldValue !== ''
        ) {
          clearErrors(name);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, getValues, clearErrors]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col h-full relative"
    >
      <div className="w-full min-w-0 overflow-x-auto flex-1 mb-2 pl-2 pb-16">
        <PrescriptionTable
          fields={fields}
          control={control}
          isReadOnly={isReadOnly}
          selectedMedicines={selectedMedicines}
          handleRemove={handleRemove}
          clearErrors={clearErrors}
          setValue={setValue}
        />
        <FieldValidationError errors={errors} fieldKey="prescription" />
      </div>

      <Box
        className={cn('fixed bottom-10 left-105 z-50', {
          hidden: mode === VIEW,
        })}
      >
        <div className="capitalize px-8 text-black-600 bg-white border border-gray-400 text-sm py-2 rounded">
          Total Cost: ₹ {totalCost}
        </div>
      </Box>

      <Box
        gap={3}
        className={cn('fixed bottom-10 right-80 flex justify-end z-50', {
          hidden: mode === VIEW,
        })}
      >
        <PrimaryButton
          type="button"
          className="capitalize px-8  text-gray-600 bg-white border border-gray-400 text-sm hover:bg-gray-50"
          onClick={handleClearClick}
        >
          Clear All
        </PrimaryButton>
        <PrimaryButton
          type="button"
          onClick={handleSaveClick}
          className="capitalize px-8  text-white bg-gray-600 border border-gray-600 text-sm hover:bg-gray-700"
        >
          Save Changes
        </PrimaryButton>
        <ConfirmationModal
          open={modalType === 'save'}
          onConfirm={handleSaveConfirm}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
        <ClearConfirmationModal
          open={modalType === 'clear'}
          onConfirm={handleClearConfirm}
          onCancel={handleCancel}
        />
      </Box>
    </form>
  );
};

export default NewPrescription;
