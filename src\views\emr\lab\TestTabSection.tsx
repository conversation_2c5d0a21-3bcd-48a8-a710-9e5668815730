import React, { ReactNode, useEffect, useMemo } from 'react';

import { cn } from '@/lib/utils';

import { useTestStore } from '@/store/emr/lab/reports-store';

import { TestTabKey, labTestTabs } from '@/constants/emr/lab';

import { testReportTabs } from '@/types/emr/lab';

import NewTest from './new-test';
import PackageSelector from './packages';
import TestResult from './test-result';

import TestSearch from './new-test/TestSearch';
import TestResultFilter from './test-result/TestResultFilter';

const { NEW_TEST, TEST_RESULTS } = testReportTabs;

const TestTabSection = () => {
  const { activeTab, setActiveTab, clearSelectedLabTest, fetchLabDepartments } =
    useTestStore();

  useEffect(() => {
    fetchLabDepartments();
  }, [fetchLabDepartments]);

  const testTabs: Record<TestTabKey, ReactNode> = useMemo(
    () => ({
      [NEW_TEST]: <NewTest />,
      [TEST_RESULTS]: <TestResult />,
    }),
    []
  );

  const testSearchComponents: Record<TestTabKey, ReactNode> = useMemo(
    () => ({
      [NEW_TEST]: <TestSearch pageKey={activeTab} />,
      [TEST_RESULTS]: <TestResultFilter />,
    }),
    [activeTab]
  );

  return (
    <div className="flex flex-col h-full ">
      <div className="border-b">
        <div className="flex justify-evenly border-b shadow-md rounded-b-md">
          {labTestTabs.map(({ key, label }) => (
            <button
              key={key}
              onClick={() => {
                setActiveTab(key);
                if (key === NEW_TEST) {
                  clearSelectedLabTest();
                }
              }}
              className={cn(
                'py-2 px-6 border-b-2 transition-colors text-sm',
                activeTab === key
                  ? 'border-[#53BDF5]'
                  : 'border-transparent text-gray-500'
              )}
            >
              {label}
            </button>
          ))}
        </div>

        <div className="flex flex-col md:flex-row gap-2 p-2 items-start md:items-center">
          <div>
            <PackageSelector />
          </div>

          <div className="w-full flex items-center space-x-1 flex-wrap">
            {testSearchComponents[activeTab as TestTabKey]}
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto ">
        {testTabs[activeTab as TestTabKey]}
      </div>
    </div>
  );
};

export default TestTabSection;
