import React, { memo, useState } from 'react';

import { useFormContext } from 'react-hook-form';

import { toast } from 'sonner';

import { useAbha } from '@/store/mrd/manage-patient/manage/abha-store';

import { states } from '@/utils/constants/master';
import {
  formatToAlphaNumeric,
  formatToAlphaNumericToUpperCase,
  formatToNumber,
  formatToNumberWithDot,
} from '@/utils/format-value';

import { countryOptions } from '@/constants/mrd/manage-patient/country';
import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import AppModal from '@/core/components/app-modal';
import AppTitle from '@/core/components/app-title';
import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const GeneralDetails = () => {
  const { control, watch } = useFormContext<PatientDetails>();
  const [modalType, setModalType] = useState<
    'otp' | 'find-abha' | 'generate-abha' | 'success' | null
  >(null);
  const [otpValues, setOtpValues] = useState(['', '', '', '', '', '']);
  const [mobileNumber, setMobileNumber] = useState('');
  const [abhaNumber, setAbhaNumber] = useState('');
  const [currentFlow, setCurrentFlow] = useState<'abha' | 'aadhaar' | null>(
    null
  );

  const abhaNumberValue = watch('proof.abhaNumber');

  const {
    isSendingAbhaOtp,
    isVerifyingAbhaOtp,
    isSendingAadhaarOtp,
    isVerifyingAadhaarOtp,
    abhaOtpRequest,
    aadhaarOtpRequest,
    sendAbhaOtp,
    verifyAbhaOtp,
    sendAadhaarOtp,
    verifyAadhaarOtp,
    sendAbhaOtpByMobile,
    verifyAbhaOtpByMobile,
    resetAbhaState,
  } = useAbha();

  const handleCloseModal = () => {
    setModalType(null);
    setOtpValues(['', '', '', '', '', '']);
    setMobileNumber('');
    setAbhaNumber('');
    setCurrentFlow(null);
    resetAbhaState();
  };

  const handleGetPatientDetails = async () => {
    if (abhaNumberValue) {
      try {
        setCurrentFlow('abha');
        await sendAbhaOtp(abhaNumberValue);
        setModalType('otp');
        toast.success('OTP sent successfully to registered mobile number');
      } catch (_error) {
        toast.error('Failed to send OTP. Please try again.');
      }
    }
  };

  const handleFindAbhaNumber = () => {
    setModalType('find-abha');
  };

  const handleGenerateAbha = () => {
    setModalType('generate-abha');
  };

  const handleSendAadhaarOtp = async () => {
    if (!abhaNumber) {
      toast.error('Please enter Aadhaar number');
      return;
    }

    try {
      setCurrentFlow('aadhaar');
      await sendAadhaarOtp(abhaNumber, mobileNumber);
      setModalType('otp');
      toast.success('OTP sent successfully to registered mobile number');
    } catch (_error) {
      toast.error('Failed to send OTP. Please try again.');
    }
  };

  const handleSendAbhaOtp = async () => {
    if (!mobileNumber) {
      toast.error('Please enter mobile number');
      return;
    }

    try {
      setCurrentFlow('abha');
      await sendAbhaOtpByMobile(mobileNumber);
      setModalType('otp');
      toast.success('OTP sent successfully');
    } catch (_error) {
      toast.error('Failed to send OTP. Please try again.');
    }
  };

  const handleVerifyOtp = async () => {
    const otp = otpValues.join('');
    if (otp.length !== 6) {
      toast.error('Please enter complete OTP');
      return;
    }

    try {
      if (currentFlow === 'abha' && abhaOtpRequest) {
        if (abhaNumberValue) {
          // ABHA number verification
          await verifyAbhaOtp({
            authMethod: 'MOBILE_OTP',
            healthid: abhaNumberValue || '',
            requestId: abhaOtpRequest.requestId,
            transactionId: abhaOtpRequest.requestId, // Assuming same as requestId
          });
        } else {
          // Mobile-based ABHA verification
          await verifyAbhaOtpByMobile(otp, abhaOtpRequest.requestId);
        }
        toast.success('ABHA verification successful');
        setModalType('success');
      } else if (currentFlow === 'aadhaar' && aadhaarOtpRequest) {
        await verifyAadhaarOtp(abhaNumber, otp, aadhaarOtpRequest.txnId);
        toast.success('Aadhaar verification successful');
        setModalType('success');
      }
    } catch (_error) {
      toast.error('Invalid OTP. Please try again.');
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtpValues = [...otpValues];
      newOtpValues[index] = value;
      setOtpValues(newOtpValues);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

  const handleResendOtp = async () => {
    try {
      if (currentFlow === 'abha') {
        if (abhaNumberValue) {
          await sendAbhaOtp(abhaNumberValue);
        } else if (mobileNumber) {
          await sendAbhaOtpByMobile(mobileNumber);
        }
        toast.success('OTP resent successfully');
      } else if (currentFlow === 'aadhaar' && abhaNumber && mobileNumber) {
        await sendAadhaarOtp(abhaNumber, mobileNumber);
        toast.success('OTP resent successfully');
      }
    } catch (_error) {
      toast.error('Failed to resend OTP. Please try again.');
    }
  };

  return (
    <div className="flex flex-col gap-base w-full max-h-full h-full overflow-y-auto">
      {/* ABHA Number Section */}
      <div className="w-full flex flex-col gap-base py-base border-b-2">
        <div className="flex  gap-base w-[60%] items-end">
          <div className="w-1/2">
            <ControlledTextField
              name="proof.abhaNumber"
              control={control}
              label="ABHA Number"
              placeholder="0000 0000 000"
              fullWidth
              initiallyReadonly
              // formatValue={formatToAbha}
              slotProps={{
                input: {
                  inputProps: { maxLength: 14 },
                },
              }}
            />
          </div>
          <div className="flex-shrink-0">
            <AppButton
              variant="contained"
              onClick={handleGetPatientDetails}
              disabled={!abhaNumberValue || isSendingAbhaOtp}
              sx={{ minWidth: 180 }}
              endIcon={<AppIcon icon="material-symbols:keyboard-arrow-right" />}
            >
              {isSendingAbhaOtp ? 'Sending OTP...' : 'Get Patient Details'}
            </AppButton>
          </div>
        </div>
        <div className="text-sm text-gray-600">
          Don&apos;t have an ABHA Number?{' '}
          <button
            type="button"
            onClick={handleGenerateAbha}
            className="text-[#0496E1] hover:underline"
          >
            Click here
          </button>{' '}
          |{' '}
          <button
            type="button"
            onClick={handleFindAbhaNumber}
            className="text-[#0496E1] hover:underline"
          >
            Find ABHA using mobile number
          </button>
        </div>
      </div>

      {/* Basic Details Header */}
      <AppTitle variant="subtitle1" className="pt-base">
        Basic Details
      </AppTitle>

      <div className="w-full flex flex-col gap-base border-b-2 py-base">
        <div className="flex gap-base w-[60%]">
          <div className="w-1/2">
            <ControlledTextField
              name="name"
              control={control}
              label="Patient Name"
              placeholder="Enter Patient Name"
              fullWidth
              required
              initiallyReadonly
              formatValue={formatToAlphaNumericToUpperCase}
            />
          </div>
          <div className="w-1/4">
            <ControlledDatePicker
              name="dob"
              control={control}
              label="Date of Birth"
              required
              initiallyReadonly
            />
          </div>
          <div className="w-1/4">
            <ControlledSelectField
              name="sex"
              control={control}
              label="Gender"
              options={genderOptions}
              placeholder="Select"
              required
              initiallyReadonly
            />
          </div>
        </div>
        <div className="flex gap-base w-[40%]">
          <ControlledTextField
            name="height"
            control={control}
            label="Height"
            placeholder="Height in cms"
            fullWidth
            formatValue={formatToNumber}
          />
          <ControlledTextField
            name="weight"
            control={control}
            label="Weight"
            placeholder="Weight in kgs"
            fullWidth
            formatValue={formatToNumberWithDot(1)}
          />
          <ControlledSelectField
            name="maritalStatus"
            control={control}
            label="Marital Status"
            options={maritalStatusOptions}
            placeholder="Select"
          />
        </div>
      </div>
      <div className="w-[60%] flex flex-col gap-base py-base">
        <AppTitle variant="subtitle1">Patient Address</AppTitle>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.houseName"
            control={control}
            label="House Name"
            placeholder="House No/Name"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.pin"
            control={control}
            label="Pin"
            placeholder="682030"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            slotProps={{
              input: {
                inputProps: { maxLength: 6 },
              },
            }}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.street"
            control={control}
            label="Street"
            placeholder="Street"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.city"
            control={control}
            label="City"
            placeholder="City"
            required
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.district"
            control={control}
            label="District"
            placeholder="District"
            initiallyReadonly
          />
          <ControlledSelectField
            name="address.state"
            control={control}
            label="State"
            options={states}
            placeholder="State"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-1/2">
          <ControlledSelectField
            name="address.country"
            control={control}
            label="Country"
            options={countryOptions}
            placeholder="Country"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="contact.phone"
            control={control}
            label="Mobile (Personal)"
            placeholder="Phone"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            required
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
          />
          <ControlledTextField
            name="contact.email"
            control={control}
            label="Email"
            placeholder="Email"
            fullWidth
            initiallyReadonly
          />
        </div>
      </div>

      {/* OTP Verification Modal */}
      <AppModal
        open={modalType === 'otp'}
        onClose={handleCloseModal}
        title="OTP Verification"
        classes={{
          root: 'w-96 max-w-sm',
        }}
      >
        <div className="flex flex-col gap-4 p-6">
          <p className="text-sm text-gray-600">
            Enter the verification code we just sent on your Mobile Number
          </p>
          <div className="flex gap-2 justify-center">
            {otpValues.map((value, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                value={value}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleOtpKeyDown(index, e)}
                className="w-12 h-12 text-center border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={1}
              />
            ))}
          </div>
          <div className="text-sm text-center">
            Didn&apos;t receive code?{' '}
            <button
              className="text-[#0496E1] hover:underline"
              onClick={handleResendOtp}
              disabled={isSendingAbhaOtp || isSendingAadhaarOtp}
            >
              Resend
            </button>
          </div>
          <div className="flex gap-3 w-full mt-4">
            <AppButton
              variant="outlined"
              onClick={handleCloseModal}
              className="flex-1"
            >
              Cancel
            </AppButton>
            <AppButton
              variant="contained"
              onClick={handleVerifyOtp}
              disabled={isVerifyingAbhaOtp || isVerifyingAadhaarOtp}
              className="flex-1"
            >
              {isVerifyingAbhaOtp || isVerifyingAadhaarOtp
                ? 'Verifying...'
                : 'Submit'}
            </AppButton>
          </div>
        </div>
      </AppModal>

      {/* Find ABHA Number Modal */}
      <AppModal
        open={modalType === 'find-abha'}
        onClose={handleCloseModal}
        title="Find ABHA Number"
        classes={{
          root: 'w-96 max-w-sm',
        }}
      >
        <div className="flex flex-col gap-4 p-6">
          <p className="text-sm text-gray-600">
            Enter the mobile number registered with your ABHA ID
          </p>
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Mobile (Personal)*
            </label>
            <input
              type="text"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(formatToNumber(e.target.value))}
              placeholder="0987654321"
              maxLength={10}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex gap-3 w-full mt-4">
            <AppButton
              variant="outlined"
              onClick={handleCloseModal}
              className="flex-1"
            >
              Cancel
            </AppButton>
            <AppButton
              variant="contained"
              onClick={handleSendAbhaOtp}
              disabled={isSendingAbhaOtp}
              className="flex-1"
            >
              {isSendingAbhaOtp ? 'Sending...' : 'Send OTP'}
            </AppButton>
          </div>
        </div>
      </AppModal>

      {/* Generate ABHA Number Modal */}
      <AppModal
        open={modalType === 'generate-abha'}
        onClose={handleCloseModal}
        title="Generate ABHA Number"
        classes={{
          root: 'w-96 max-w-sm',
        }}
      >
        <div className="flex flex-col gap-4 p-6">
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Aadhar Number
            </label>
            <input
              type="text"
              value={abhaNumber}
              onChange={(e) => setAbhaNumber(e.target.value)}
              placeholder="0000 0000 000"
              maxLength={14}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Mobile (Personal)*
            </label>
            <input
              type="text"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(formatToNumber(e.target.value))}
              placeholder="0987654321"
              maxLength={10}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex gap-3 w-full mt-4">
            <AppButton
              variant="outlined"
              onClick={handleCloseModal}
              className="flex-1"
            >
              Cancel
            </AppButton>
            <AppButton
              variant="contained"
              onClick={handleSendAadhaarOtp}
              disabled={isSendingAadhaarOtp}
              className="flex-1"
            >
              {isSendingAadhaarOtp ? 'Sending...' : 'Send OTP'}
            </AppButton>
          </div>
        </div>
      </AppModal>

      {/* Success Modal */}
      <AppModal
        open={modalType === 'success'}
        onClose={handleCloseModal}
        title=""
        classes={{
          root: 'w-96 max-w-sm',
        }}
      >
        <div className="flex flex-col items-center gap-6 p-8">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <AppIcon
              icon="material-symbols:check-circle"
              className="text-white text-2xl"
            />
          </div>
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">
              OTP Verified Successfully!
            </h2>
            <p className="text-gray-600">
              Your mobile number has been verified, and your ABHA number has
              been generated successfully.
            </p>
          </div>
          <AppButton
            variant="contained"
            onClick={handleCloseModal}
            className="w-full"
          >
            Continue
          </AppButton>
        </div>
      </AppModal>
    </div>
  );
};

export default memo(GeneralDetails);
