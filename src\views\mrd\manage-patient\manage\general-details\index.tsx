import React, { memo, useState } from 'react';

import { useFormContext } from 'react-hook-form';

import { MdOutlineKeyboardArrowRight } from 'react-icons/md';

import { states } from '@/utils/constants/master';
import {
  formatToAlphaNumeric,
  formatToAlphaNumericToUpperCase,
  formatToNumber,
  formatToNumberWithDot,
} from '@/utils/format-value';

import { countryOptions } from '@/constants/mrd/manage-patient/country';
import {
  genderOptions,
  maritalStatusOptions,
} from '@/constants/mrd/manage-patient/select-options';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';
import AppTitle from '@/core/components/app-title';
import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const GeneralDetails = () => {
  const { control, watch } = useFormContext<PatientDetails>();
  const [modalType, setModalType] = useState<
    'otp' | 'find-abha' | 'generate-abha' | null
  >(null);
  const [otpValues, setOtpValues] = useState(['', '', '', '', '', '']);
  const [mobileNumber, setMobileNumber] = useState('');
  const [abhaNumber, setAbhaNumber] = useState('');

  const abhaNumberValue = watch('proof.abhaNumber');

  const handleCloseModal = () => {
    setModalType(null);
    setOtpValues(['', '', '', '', '', '']);
    setMobileNumber('');
    setAbhaNumber('');
  };

  const handleGetPatientDetails = () => {
    if (abhaNumberValue) {
      setModalType('otp');
    }
  };

  const handleFindAbhaNumber = () => {
    setModalType('find-abha');
  };

  const handleGenerateAbha = () => {
    setModalType('generate-abha');
  };

  const handleSendOtp = () => {
    setModalType('otp');
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtpValues = [...otpValues];
      newOtpValues[index] = value;
      setOtpValues(newOtpValues);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

  return (
    <div className="flex flex-col gap-base w-full max-h-full h-full overflow-y-auto">
      {/* ABHA Number Section */}
      <div className="w-full flex flex-col gap-base py-base border-b-2">
        <div className="flex  gap-base w-[60%] items-end">
          <div className="w-1/2">
            <ControlledTextField
              name="proof.abhaNumber"
              control={control}
              label="ABHA Number"
              placeholder="0000 0000 000"
              fullWidth
              initiallyReadonly
              // formatValue={formatToAbha}
              slotProps={{
                input: {
                  inputProps: { maxLength: 14 },
                },
              }}
            />
          </div>
          <div className="flex-shrink-0">
            <AppButton
              variant="contained"
              onClick={handleGetPatientDetails}
              disabled={!abhaNumberValue}
              sx={{ minWidth: 180 }}
              // className="h-10"
              endIcon={<MdOutlineKeyboardArrowRight />}
            >
              Get Patient Details
            </AppButton>
          </div>
        </div>
        <div className="text-sm text-gray-600">
          Don&apos;t have an ABHA Number?{' '}
          <button
            type="button"
            onClick={handleGenerateAbha}
            className="text-[#0496E1] hover:underline"
          >
            Click here
          </button>{' '}
          |{' '}
          <button
            type="button"
            onClick={handleFindAbhaNumber}
            className="text-[#0496E1] hover:underline"
          >
            Find ABHA using mobile number
          </button>
        </div>
      </div>

      {/* Basic Details Header */}
      <AppTitle variant="subtitle1" className="pt-base">
        Basic Details
      </AppTitle>

      <div className="w-full flex flex-col gap-base border-b-2 py-base">
        <div className="flex gap-base w-[60%]">
          <div className="w-1/2">
            <ControlledTextField
              name="name"
              control={control}
              label="Patient Name"
              placeholder="Enter Patient Name"
              fullWidth
              required
              initiallyReadonly
              formatValue={formatToAlphaNumericToUpperCase}
            />
          </div>
          <div className="w-1/4">
            <ControlledDatePicker
              name="dob"
              control={control}
              label="Date of Birth"
              required
              initiallyReadonly
            />
          </div>
          <div className="w-1/4">
            <ControlledSelectField
              name="sex"
              control={control}
              label="Gender"
              options={genderOptions}
              placeholder="Select"
              required
              initiallyReadonly
            />
          </div>
        </div>
        <div className="flex gap-base w-[40%]">
          <ControlledTextField
            name="height"
            control={control}
            label="Height"
            placeholder="Height in cms"
            fullWidth
            formatValue={formatToNumber}
          />
          <ControlledTextField
            name="weight"
            control={control}
            label="Weight"
            placeholder="Weight in kgs"
            fullWidth
            formatValue={formatToNumberWithDot(1)}
          />
          <ControlledSelectField
            name="maritalStatus"
            control={control}
            label="Marital Status"
            options={maritalStatusOptions}
            placeholder="Select"
          />
        </div>
      </div>
      <div className="w-[60%] flex flex-col gap-base py-base">
        <AppTitle variant="subtitle1">Patient Address</AppTitle>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.houseName"
            control={control}
            label="House Name"
            placeholder="House No/Name"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.pin"
            control={control}
            label="Pin"
            placeholder="682030"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            slotProps={{
              input: {
                inputProps: { maxLength: 6 },
              },
            }}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.street"
            control={control}
            label="Street"
            placeholder="Street"
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
          <ControlledTextField
            name="address.city"
            control={control}
            label="City"
            placeholder="City"
            required
            fullWidth
            initiallyReadonly
            formatValue={formatToAlphaNumeric}
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="address.district"
            control={control}
            label="District"
            placeholder="District"
            initiallyReadonly
          />
          <ControlledSelectField
            name="address.state"
            control={control}
            label="State"
            options={states}
            placeholder="State"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-1/2">
          <ControlledSelectField
            name="address.country"
            control={control}
            label="Country"
            options={countryOptions}
            placeholder="Country"
            initiallyReadonly
          />
        </div>
        <div className="flex gap-base w-full">
          <ControlledTextField
            name="contact.phone"
            control={control}
            label="Mobile (Personal)"
            placeholder="Phone"
            fullWidth
            initiallyReadonly
            formatValue={formatToNumber}
            required
            slotProps={{
              input: {
                inputProps: { maxLength: 10 },
              },
            }}
          />
          <ControlledTextField
            name="contact.email"
            control={control}
            label="Email"
            placeholder="Email"
            fullWidth
            initiallyReadonly
          />
        </div>
      </div>

      {/* OTP Verification Modal */}
      <AppModal
        open={modalType === 'otp'}
        onClose={handleCloseModal}
        title="OTP Verification"
      >
        <div className="flex flex-col gap-4 p-4">
          <p className="text-sm text-gray-600">
            Enter the verification code we just sent on your Mobile Number
          </p>
          <div className="flex gap-2 justify-center">
            {otpValues.map((value, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                value={value}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleOtpKeyDown(index, e)}
                className="w-12 h-12 text-center border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={1}
              />
            ))}
          </div>
          <div className="text-sm text-center">
            Didn&apos;t receive code?{' '}
            <button className="text-blue-600 hover:underline">Resend</button>
          </div>
          <div className="flex gap-3 justify-end mt-4">
            <AppButton variant="outlined" onClick={handleCloseModal}>
              Cancel
            </AppButton>
            <AppButton variant="contained">Submit</AppButton>
          </div>
        </div>
      </AppModal>

      {/* Find ABHA Number Modal */}
      <AppModal
        open={modalType === 'find-abha'}
        onClose={handleCloseModal}
        title="Find ABHA Number"
      >
        <div className="flex flex-col gap-4 p-4">
          <p className="text-sm text-gray-600">
            Enter the mobile number registered with your ABHA ID
          </p>
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Mobile (Personal)*
            </label>
            <input
              type="text"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(formatToNumber(e.target.value))}
              placeholder="0987654321"
              maxLength={10}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex gap-3 justify-end mt-4">
            <AppButton variant="outlined" onClick={handleCloseModal}>
              Cancel
            </AppButton>
            <AppButton variant="contained" onClick={handleSendOtp}>
              Send OTP
            </AppButton>
          </div>
        </div>
      </AppModal>

      {/* Generate ABHA Number Modal */}
      <AppModal
        open={modalType === 'generate-abha'}
        onClose={handleCloseModal}
        title="Generate ABHA Number"
      >
        <div className="flex flex-col gap-4 p-4">
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Aadhar Number
            </label>
            <input
              type="text"
              value={abhaNumber}
              onChange={(e) => setAbhaNumber(e.target.value)}
              placeholder="0000 0000 000"
              maxLength={14}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-col gap-1">
            <label className="text-sm font-medium text-gray-700">
              Mobile (Personal)*
            </label>
            <input
              type="text"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(formatToNumber(e.target.value))}
              placeholder="0987654321"
              maxLength={10}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex gap-3 justify-end mt-4">
            <AppButton variant="outlined" onClick={handleCloseModal}>
              Cancel
            </AppButton>
            <AppButton variant="contained" onClick={handleSendOtp}>
              Send OTP
            </AppButton>
          </div>
        </div>
      </AppModal>
    </div>
  );
};

export default memo(GeneralDetails);
