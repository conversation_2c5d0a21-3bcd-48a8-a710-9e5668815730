import { Conversation } from '@/query/speech';

export interface TranscriptProps {
  conversation?: Conversation[];
}

export default function Transcript({ conversation }: TranscriptProps) {
  if (!conversation || !conversation?.length) {
    return <div>No transcript generated!</div>;
  }

  return (
    <div className="w-full">
      <div className="flex flex-col gap-5">
        {conversation?.map((c) => (
          <div key={c?.message?.slice(0, 5) + c?.speaker}>
            <h4 className="text-base font-medium capitalize">{c?.speaker}:</h4>
            <p className="mt-2">{c?.message}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
