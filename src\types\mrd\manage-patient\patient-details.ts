import { BaseOption } from '@/types';

export type Proof = {
  type?: BaseOption | null;
  url?: string;
  aadharNumber?: string;
  abhaNumber?: string;
};

export type Contact = {
  phone: string;
  email?: string;
};

export type PatientAddress = {
  houseName?: string;
  pin?: string;
  street?: string;
  city: string;
  state?: BaseOption | null;
  country?: BaseOption | null;
  post?: string;
  district?: string;
};

export type Insurance = {
  provider?: BaseOption | null;
  id?: string;
  url?: string;
};

export type PatientDetails = {
  id?: string;
  name: string;
  dob: string;
  sex: BaseOption | null;
  height?: string;
  weight?: string;
  maritalStatus: BaseOption | null;
  address: PatientAddress;
  contact: Contact;
  proof?: Proof;
  insurance?: Insurance;
};

export const patientDetailsDefaultData: PatientDetails = {
  name: '',
  dob: '',
  sex: null,
  height: '',
  weight: '',
  maritalStatus: null,
  address: {
    city: '',
    state: null,
    country: null,
  },
  contact: {
    phone: '',
  },
  proof: {
    type: null,
  },
  insurance: {
    provider: null,
  },
};
