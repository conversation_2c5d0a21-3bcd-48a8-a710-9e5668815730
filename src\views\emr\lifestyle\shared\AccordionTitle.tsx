import React, { FC, memo, useCallback, useMemo } from 'react';

import { cn } from '@/lib/utils';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';

type Props = {
  expand?: boolean;
  doctorName?: string;
  finalised?: boolean;
  onExpand?: () => void;
  onFinalise?: () => void;
  onPrint?: () => void;
  open?: boolean;
  stepper?: string[];
  designation?: string;
  department?: string;
  date?: string;
};

const AccordionTitle: FC<Props> = ({
  expand,
  doctorName,
  finalised,
  onExpand,
  onFinalise,
  onPrint,
  open,
  stepper,
  designation,
  department,
  date,
}) => {
  const doctorFirstLetter = useMemo(() => {
    return (
      doctorName
        ?.split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2) || '--'
    );
  }, [doctorName]);

  const formIndicator = useMemo(() => {
    if (!stepper?.length) return null;

    return stepper.map((step, index) => (
      <div
        key={index}
        className="hidden items-center gap-1 text-black whitespace-nowrap 2xl:flex"
      >
        <span className="truncate max-w-[200px]">{step}</span>
        {index < stepper.length - 1 && (
          <AppIcon className="text-gray-600" icon="si:arrow-right-fill" />
        )}
      </div>
    ));
  }, [stepper]);

  const renderFields = useCallback((value?: React.ReactNode) => {
    if (!value) return null;
    return (
      <div
        title={typeof value === 'string' ? value : ''}
        className="flex items-center text-[#001926] text-base font-normal leading-none whitespace-nowrap truncate"
      >
        {value}
      </div>
    );
  }, []);
  return (
    <>
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <AppIconButton
            variant="outlined"
            sx={{ backgroundColor: 'white !important' }}
          >
            {expand ? (
              <div className="w-10 h-10 rounded-full bg-transparent flex items-center justify-center text-[#6B7280] font-semibold text-base">
                {doctorFirstLetter}
              </div>
            ) : (
              <AppIcon
                icon="mingcute:down-fill"
                className={cn('!transition-transform !duration-200 text-base', {
                  'transform -rotate-90': !open,
                })}
              />
            )}
          </AppIconButton>
          <span className="font-archivo font-medium text-xl text-[#001926] leading-none">
            {doctorName}
          </span>
        </div>
        <div className="flex items-center gap-6 w-full flex-1 min-w-0 overflow-hidden">
          {renderFields(designation)}
          {renderFields(department)}
          {renderFields(formatDate(date, DateFormats.DATE_DD_MM_YYYY_SLASH))}
          {renderFields(formIndicator)}
        </div>
      </div>
      <div className="flex items-center gap-2 w-35 justify-end">
        <AppIconButton
          onClick={(e) => {
            e.stopPropagation();
            onFinalise?.();
          }}
          disabled={finalised}
          sx={{ backgroundColor: 'white !important' }}
          variant="outlined"
        >
          <AppIcon
            className="text-black p-0.5"
            icon={finalised ? 'gg:lock' : 'qlementine-icons:unlock-16'}
          />
        </AppIconButton>

        <AppIconButton
          onClick={(e) => {
            e.stopPropagation();
            onPrint?.();
          }}
          variant="outlined"
          sx={{ backgroundColor: 'white !important' }}
        >
          <AppIcon className="p-0.5" icon="streamline:printer" />
        </AppIconButton>
        <AppIconButton
          onClick={(e) => {
            e.stopPropagation();
            onExpand?.();
          }}
          variant="outlined"
          sx={{ backgroundColor: 'white !important' }}
        >
          <AppIcon
            icon={
              expand
                ? 'stash:shrink-diagonal'
                : 'material-symbols:expand-content'
            }
            className="p-0.5"
          />
        </AppIconButton>
      </div>
    </>
  );
};

export default memo(AccordionTitle);
