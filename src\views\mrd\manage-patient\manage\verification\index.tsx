import { memo } from 'react';

import { useFormContext } from 'react-hook-form';

import { formatAadhar } from '@/utils/mrd/manage-patient/aadhar';

import { proofOptions } from '@/constants/mrd/manage-patient/select-options';

import ControlledImageUploader from '@/components/controlled-inputs/ControlledImageUploader';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTextField from '@/components/controlled-inputs/ControlledTextField';

import { PatientDetails } from '@/types/mrd/manage-patient/patient-details';

const Verification = () => {
  const { control } = useFormContext<PatientDetails>();
  return (
    <div className="w-full h-full flex flex-col gap-base py-base">
      <div className="w-[85%] flex gap-base">
        <ControlledSelectField
          name="proof.type"
          control={control}
          label="Proof Type"
          placeholder="Select"
          options={proofOptions}
          initiallyReadonly
        />
        <ControlledTextField
          name="proof.aadharNumber"
          control={control}
          label="Aadhar Number"
          placeholder="0000 0000 000"
          initiallyReadonly
          fullWidth
          formatValue={formatAadhar}
          slotProps={{
            input: {
              inputProps: { maxLength: 14 },
            },
          }}
        />
        <ControlledTextField
          name="proof.abhaNumber"
          control={control}
          label="Abha Number (optional)"
          placeholder="0000 0000 000"
          initiallyReadonly
          fullWidth
          slotProps={{
            input: {
              inputProps: { maxLength: 14 },
            },
          }}
        />
      </div>
      <div className="w-[85%] flex gap-base">
        <ControlledImageUploader
          name="proof.url"
          control={control}
          label="ID Proof"
          accept=".png, .jpg, .jpeg, .pdf"
          maxSizeInMB={5}
          indicationLabel="(The file size must be less than 5MB.)"
          showError={false}
        />
      </div>
    </div>
  );
};

export default memo(Verification);
