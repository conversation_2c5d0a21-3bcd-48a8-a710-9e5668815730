'use client';

import { memo } from 'react';

interface ViewData {
  disease: string;
  treatmentHistory: string;
  yearOfDiagnosis: string;
  diagnosisDuration: string;
  durationUnit: string;
  status: 'active' | 'inactive';
  smokingHistory: 'no' | 'former' | 'current';
  smokingPackYears: string;
  smokingFrequency: string;
  alcoholConsumption: 'no' | 'former' | 'current';
  oralTobacco: 'no' | 'former' | 'current';
  drugs: 'no' | 'former' | 'current';
}

interface MedicalHistoryAddictionViewProps {
  data?: ViewData;
}

const MedicalHistoryAddictionView = ({
  data,
}: MedicalHistoryAddictionViewProps) => {
  // Mock data for demonstration - replace with actual data
  const viewData = data || {
    disease: 'Hypertension',
    treatmentHistory:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
    yearOfDiagnosis: '2022',
    diagnosisDuration: '6 Months',
    durationUnit: 'Months',
    status: 'active' as const,
    smokingHistory: 'no' as const,
    smokingPackYears: '',
    smokingFrequency: '',
    alcoholConsumption: 'no' as const,
    oralTobacco: 'no' as const,
    drugs: 'no' as const,
  };

  const formatHistoryValue = (value: string) => {
    switch (value) {
      case 'no':
        return 'No';
      case 'former':
        return 'Former';
      case 'current':
        return 'Current';
      default:
        return '-';
    }
  };

  return (
    <div className="flex flex-col h-full p-6 space-y-6">
      {/* Diagnosis Section */}
      <div className="space-y-4">
        {/* Header Row */}
        <div className="grid grid-cols-4 gap-8">
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Diagnosis</h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">
              Year of Diagnosis
            </h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">
              Diagnosis Duration
            </h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Status</h3>
          </div>
        </div>

        {/* Data Row */}
        <div className="grid grid-cols-4 gap-8">
          <div>
            <p className="text-sm text-gray-600">
              {viewData.disease || 'Disease Name'}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">
              {viewData.yearOfDiagnosis || '2022'}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">
              {viewData.diagnosisDuration
                ? `${viewData.diagnosisDuration} ${viewData.durationUnit}`
                : '6 Months'}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 capitalize">
              {viewData.status || 'Inactive'}
            </p>
          </div>
        </div>
      </div>

      {/* Treatment History Section */}
      <div className="space-y-3">
        <h3 className="text-sm font-semibold text-gray-900">
          Treatment History
        </h3>
        <p className="text-sm text-gray-600 leading-relaxed">
          {viewData.treatmentHistory ||
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation'}
        </p>
      </div>

      {/* Addiction History Section */}
      <div className="space-y-4">
        {/* Header Row */}
        <div className="grid grid-cols-4 gap-8">
          <div>
            <h3 className="text-sm font-semibold text-gray-900">
              Smoking History
            </h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Alcohol</h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">
              Oral Tobacco
            </h3>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Drugs</h3>
          </div>
        </div>

        {/* Data Row */}
        <div className="grid grid-cols-4 gap-8">
          <div>
            <p className="text-sm text-gray-600">
              {formatHistoryValue(viewData.smokingHistory)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">
              {formatHistoryValue(viewData.alcoholConsumption)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">
              {formatHistoryValue(viewData.oralTobacco)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600">
              {formatHistoryValue(viewData.drugs)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(MedicalHistoryAddictionView);
