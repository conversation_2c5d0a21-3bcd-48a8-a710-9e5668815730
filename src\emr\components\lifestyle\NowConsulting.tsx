import React, { FC, memo, useMemo } from 'react';

import { IoIosAdd } from 'react-icons/io';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import colors from '@/utils/colors';
import { archivo } from '@/utils/fonts';

import FluentFoodApple20Regular from '@/assets/svg/FluentFoodApple20Regular';

import { calculateAge } from '@/helpers/dates';

import { NutritionAndDietForm } from '@/constants/lifestyle';

import PatientCard from '../patient-card';

import LifestyleNote from './LifestyleNote';
import LifestyleTabs from './LifestyleTabs';
import TabPanels from './TabPanels';

type Props = {
  onAddRecordManually: () => void;
};

const NowConsulting: FC<Props> = ({ onAddRecordManually }) => {
  const { patient } = useCurrentPatientStore();
  const { setCurrentTab, currentTab, isSubmittedToday } =
    useLifestyleUtilStore();

  const tabList = useMemo(
    () => [
      {
        label: 'Nutrition & Diet',
        icon: <FluentFoodApple20Regular color="inherit" />,
        content: (
          <TabPanels
            items={NutritionAndDietForm}
            activeForm={currentTab}
            onSelectTab={setCurrentTab}
          />
        ),
      },
      // TODO: Enable these when ready
      // {
      //   label: 'Physical Activity',
      //   icon: <RunningIcon />,
      //   content: <>Physical Activity</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Mental Health',
      //   icon: <BrainIcon />,
      //   content: <>Mental Health</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Sleep',
      //   icon: <SleepIcon />,
      //   content: <>Sleep</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Meditation',
      //   icon: <MeditationIcon />,
      //   content: <>Meditation</>,
      //   disabled: true,
      // },
    ],
    [currentTab, setCurrentTab]
  );

  return (
    <div
      className={`flex-1 h-full bg-white flex flex-col rounded-base shadow-base border overflow-x-hidden overflow-y-auto w-full min-w-full border-[${colors.common.ashGray}]`}
    >
      <div className="h-fit w-full rounded border shadow-custom-xs p-1 basis-10">
        <p
          className={`px-1.5 border-b w-full font-semibold text-[20px] -tracking-[2.2%] text-[${colors.common.darkerNavyBlue}]`}
        >
          Now Consulting
        </p>
        <div className="p-1.5">
          <PatientCard
            name={patient?.name ?? '--'}
            dob={patient?.dob}
            sex={patient?.sex ?? '--'}
            address={patient?.address}
            calculateAge={calculateAge}
          />
        </div>
      </div>
      <div className="flex-1 w-full max-w-full p-2">
        <LifestyleTabs items={tabList} />
      </div>
      <div className="w-full h-[calc(100%-3rem)] p-2 overflow-y-auto">
        <LifestyleNote />
      </div>
      <div className="flex flex-col p-2 gap-1.5">
        <div className="w-full">
          <button
            className={`xl:text-sm text-xs py-1.5 xl:py-2 flex w-full items-center justify-center gap-1.5 bg-black text-white rounded-md ${archivo.className} disabled:bg-gray-300 disabled:opacity-90 disabled:cursor-not-allowed`}
            onClick={onAddRecordManually}
            disabled={isSubmittedToday}
          >
            Add Record Manually
            <IoIosAdd className="text-md" />
          </button>
        </div>
        {/* TODO: Add add record */}
        {/* <div className="w-full">
          <AddRecord onSave={() => {}} disabled />
        </div> */}
      </div>
    </div>
  );
};

export default memo(NowConsulting);
