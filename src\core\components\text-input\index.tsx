import { forwardRef, InputHTMLAttributes } from 'react';
// @deprecated This component will be removed in future versions. Please use the new Input component instead.

import { FieldError } from 'react-hook-form';

import InputLabel from '../input-label';

/**
 * @deprecated This type will be removed in future versions. Please use the new app text field component instead.
 */
export interface TextInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  errors?: FieldError;
  endDecoration?: React.ReactNode;
  inputClassName?: string;
  labelClassName?: string;
  iconClassName?: string;
  endIconSecondary?: React.ReactNode;
  fieldClassName?: string;
  disableRef?: boolean;
}

/**
 * @deprecated This component will be removed in future versions. Please use the new app text field component instead.
 */
const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      className,
      errors,
      label,
      endDecoration,
      inputClassName,
      disabled,
      labelClassName,
      iconClassName,
      endIconSecondary,
      placeholder,
      required = false,
      fieldClassName = '',
      disableRef = false,
      ...rest
    },
    ref
  ) => {
    return (
      <label className={`relative flex flex-col text-[#001926] ${className}`}>
        {label && (
          <InputLabel
            label={label}
            required={required}
            className={labelClassName}
          />
        )}
        <div className={`relative ${inputClassName}`}>
          <input
            ref={!disableRef ? ref : undefined}
            className={`rounded-md text-base py-2 px-2 md:px-4.5 pr-13  truncate border border-[#637D92] w-full focus:outline-none focus:ring-0 ${fieldClassName}`}
            disabled={disabled}
            placeholder={placeholder}
            {...rest}
          />
          {endDecoration && (
            <div
              className={`absolute right-4 top-1/2 -translate-y-1/2 ${iconClassName}`}
            >
              {endIconSecondary && endIconSecondary}
              {endDecoration}
            </div>
          )}
        </div>
        {errors?.message && (
          <span className="absolute top-[105%] left-0 text-red-500 text-xs">
            {errors.message}
          </span>
        )}
      </label>
    );
  }
);

TextInput.displayName = 'TextInput';

export default TextInput;
