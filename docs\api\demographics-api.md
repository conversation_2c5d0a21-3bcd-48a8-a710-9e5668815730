# Demographics API Documentation

## Overview

The Demographics API manages patient demographic information including personal details and contact information.

## Base URL

```
/patient/lifestyle/demographics
```

## Data Models

### Demographics

```typescript
{
  id: string;           // Unique identifier for demographics record
  name: string;         // Patient full name
  cmchId: string;       // CMCH (hospital) ID
  dob: string;          // Date of birth (ISO date string)
  age: string;          // Patient age
  sex: string;          // Patient gender/sex
  maritalStatus: string; // Marital status
  contacts: Contact[];   // Array of contact information
}
```

### Contact

```typescript
{
  phone: string;    // Phone number (required)
  email?: string;   // Email address (optional)
}
```

## API Endpoints

### 1. Get Patient Demographics

Retrieve demographics information for a specific patient.

**Endpoint:** `GET /patient/lifestyle/demographics`

**Query Parameters:**

- `patientId` (string, required) - The patient's unique identifier

**Response:**

```json
{
  "id": "demo-123",
  "name": "<PERSON>",
  "cmchId": "CMCH001234",
  "dob": "1990-05-15",
  "age": "33",
  "sex": "male",
  "maritalStatus": "married",
  "contacts": [
    {
      "phone": "+**********",
      "email": "<EMAIL>"
    }
  ]
}
```

**Status Codes:**

- `200 OK` - Demographics retrieved successfully
- `404 Not Found` - Patient not found
- `500 Internal Server Error` - Server error

### 2. Create Patient Demographics

Create new demographics record for a patient.

**Endpoint:** `POST /patient/lifestyle/demographics`

**Query Parameters:**

- `patientId` (string, required) - The patient's unique identifier

**Request Body:**

```json
{
  "name": "John Doe",
  "cmchId": "CMCH001234",
  "dob": "1990-05-15",
  "age": "33",
  "sex": "male",
  "maritalStatus": "married",
  "contacts": [
    {
      "phone": "+**********",
      "email": "<EMAIL>"
    }
  ]
}
```

**Response:**

```json
{
  "id": "demo-123",
  "name": "John Doe",
  "cmchId": "CMCH001234",
  "dob": "1990-05-15",
  "age": "33",
  "sex": "male",
  "maritalStatus": "married",
  "contacts": [
    {
      "phone": "+**********",
      "email": "<EMAIL>"
    }
  ]
}
```

**Status Codes:**

- `201 Created` - Demographics created successfully
- `400 Bad Request` - Invalid request data
- `409 Conflict` - Demographics already exists for patient
- `500 Internal Server Error` - Server error

### 3. Update Patient Demographics

Update existing demographics record.

**Endpoint:** `PUT /patient/lifestyle/demographics`

**Query Parameters:**

- `id` (string, required) - The demographics record ID

**Request Body:**

```json
{
  "id": "demo-123",
  "name": "John Doe Updated",
  "cmchId": "CMCH001234",
  "dob": "1990-05-15",
  "age": "33",
  "sex": "male",
  "maritalStatus": "single",
  "contacts": [
    {
      "phone": "+**********",
      "email": "<EMAIL>"
    },
    {
      "phone": "+**********"
    }
  ]
}
```

**Response:**

```json
{
  "id": "demo-123",
  "name": "John Doe Updated",
  "cmchId": "CMCH001234",
  "dob": "1990-05-15",
  "age": "33",
  "sex": "male",
  "maritalStatus": "single",
  "contacts": [
    {
      "phone": "+**********",
      "email": "<EMAIL>"
    },
    {
      "phone": "+**********"
    }
  ]
}
```

**Status Codes:**

- `200 OK` - Demographics updated successfully
- `400 Bad Request` - Invalid request data
- `404 Not Found` - Demographics record not found
- `500 Internal Server Error` - Server error

## Field Validation Rules

### Required Fields

- `name` - Must not be empty
- `cmchId` - Must be unique hospital identifier
- `dob` - Must be valid ISO date string
- `age` - Must be valid age string
- `sex` - Must be valid gender value
- `contacts` - Must contain at least one contact with phone number

### Optional Fields

- `email` in contacts array
- `maritalStatus` can be null/empty

### Data Constraints

- `sex` values: "male", "female", "other"
- `maritalStatus` values: "single", "married", "divorced", "widowed"
- `phone` must be valid phone number format
- `email` must be valid email format when provided
- `dob` must be in the past
- `age` should correspond to the date of birth

## Error Responses

All error responses follow this format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details if available"
  }
}
```

## Usage Examples

### JavaScript/TypeScript Client

```typescript
// Get demographics
const demographics = await fetch(
  '/patient/lifestyle/demographics?patientId=123'
).then((res) => res.json());

// Create demographics
const newDemographics = await fetch(
  '/patient/lifestyle/demographics?patientId=123',
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: 'Jane Smith',
      cmchId: 'CMCH005678',
      dob: '1985-03-20',
      age: '38',
      sex: 'female',
      maritalStatus: 'married',
      contacts: [{ phone: '+**********', email: '<EMAIL>' }],
    }),
  }
).then((res) => res.json());

// Update demographics
const updatedDemographics = await fetch(
  '/patient/lifestyle/demographics?id=demo-456',
  {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      id: 'demo-456',
      name: 'Jane Smith Updated',
      // ... other fields
    }),
  }
).then((res) => res.json());
```

## Notes

- All dates should be in ISO 8601 format (YYYY-MM-DD)
- Phone numbers should include country code when applicable
- Multiple contacts can be provided for a single patient
- The API uses query parameters for identifiers (patientId, id) rather than path parameters
