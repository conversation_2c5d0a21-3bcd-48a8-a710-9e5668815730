import { FC, memo } from 'react';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';
import SectionTitle from '@/views/emr/lifestyle/shared/SectionTitle';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
};

const DietaryRecallForm: FC<Props> = ({ formFields, readonly }) => {
  return (
    <div className={`space-y-6 p-base w-full`}>
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6 w-full">
          <SectionTitle title={section?.title} icon={section?.icon} />
          <div className="w-full">
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(DietaryRecallForm);
