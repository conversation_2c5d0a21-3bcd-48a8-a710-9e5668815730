'use client';

import { memo } from 'react';

import AppTab from '@/core/components/app-tab';

import KnowledgeTab from './knowledge-tab';

const tabs = [{ label: '', content: <KnowledgeTab /> }];

const PhysicalActivityKnowledgeView = () => {
  return (
    <div className="h-full flex flex-col p-2">
      <AppTab tabs={tabs} scrollButtons={false} />
    </div>
  );
};

export default memo(PhysicalActivityKnowledgeView);
