import React, { FC, memo } from 'react';

import { Paper, Typography } from '@mui/material';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import ExpandSimpleIcon from '@/assets/svg/ExpandSimpleIcon';

import AppIconButton from '@/core/components/app-icon-button';
import { PrescriptionHistory } from '@/types/emr/prescription';

type Props = PrescriptionHistory & {
  onExpand?: () => void;
};

const HistoryCard: FC<Props> = ({ id, updated_on, doctor, onExpand }) => {
  return (
    <Paper
      key={id}
      elevation={2}
      className="flex justify-between px-4 py-1 items-center"
    >
      <Typography>
        {formatDate(updated_on, DateFormats.DATE_DD_MM_YYYY_SLASH)}
      </Typography>
      <Typography className="min-w-40 ">{doctor}</Typography>
      <AppIconButton onClick={onExpand} variant="outlined">
        <ExpandSimpleIcon />
      </AppIconButton>
    </Paper>
  );
};

export default memo(HistoryCard);
