import { memo } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { getOrDefault } from '@/utils/common';
import { formatAddress } from '@/utils/mrd/manage-patient/format-address';
import { getInitials } from '@/utils/string';

import { calculateAge } from '@/helpers/dates';

const PatientCard = () => {
  const { patient } = useCurrentPatientStore();

  const age = patient?.dob ? calculateAge(patient.dob) : '--';
  const initials = getInitials(patient?.name);

  return (
    <div className="flex items-center gap-2 p-1 pl-2 border border-gray-200 rounded-lg bg-white shadow-md min-w-0 overflow-hidden">
      {/* Patient Avatar */}
      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 border border-gray-200">
        <span className="text-gray-600 font-medium text-sm">{initials}</span>
      </div>

      {/* Patient Info */}
      <div className="flex-1 min-w-0">
        <div className="font-semibold text-gray-900 text-base truncate">
          {getOrDefault(patient?.name, '--')}
        </div>
        <div className="text-sm flex flex-nowrap text-gray-600 w-full whitespace-nowrap">
          {age} yrs | {getOrDefault(patient?.sex?.[0], '--')} |{' '}
          <span className="truncate">
            {formatAddress(patient?.address, {
              fallback: '--',
              singleLine: true,
            })}
          </span>
        </div>
      </div>
    </div>
  );
};

export default memo(PatientCard);
