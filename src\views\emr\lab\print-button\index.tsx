import { FC, memo } from 'react';

import { BiExpandAlt } from 'react-icons/bi';

import { useLabPrintStore } from '@/store/emr/lab/print-store';

import AppIconButton from '@/core/components/app-icon-button';
import { AppIconButtonProps } from '@/core/components/app-icon-button/type';
import AppModal from '@/core/components/app-modal';
import { TestResultItem } from '@/types/emr/lab';

import PrintContent from './PrintContent';

type Props = {
  disabled?: boolean;
  iconButtonProps?: Omit<AppIconButtonProps, 'children'>;
  testItem: TestResultItem;
};

const PrintButton: FC<Props> = ({ disabled, iconButtonProps, testItem }) => {
  const { onExpand, printItem, onClose } = useLabPrintStore();

  return (
    <>
      <AppIconButton
        disabled={disabled}
        variant="outlined"
        onClick={() => onExpand(testItem)}
        {...iconButtonProps}
      >
        <BiExpandAlt />
      </AppIconButton>
      <AppModal
        open={!!printItem}
        onClose={onClose}
        title="Preview"
        classes={{
          root: 'w-[60vw] h-[90vh]',
          body: 'p-0 h-[80vh]',
          backdrop: '!opacity-20',
        }}
      >
        <PrintContent />
      </AppModal>
    </>
  );
};

export default memo(PrintButton);
