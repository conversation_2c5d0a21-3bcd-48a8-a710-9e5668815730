import React from 'react';

import { Box, IconButton } from '@mui/material';

import EditableText from '@/lib/common/editable_text';

import ExpandNoteIcon from '@/assets/svg/ExpandNoteIcon';

import { GroupedRecords, StyledTypography } from './Common';

// Helper function to format date from timestamp
const formatDate = (timestamp: string) => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

interface DiagnosisCardProps {
  diagnosisRecords: GroupedRecords;
  onExpand?: () => void;
  disableExpand?: boolean;
}

const DiagnosisCard: React.FC<DiagnosisCardProps> = ({
  diagnosisRecords,
  onExpand,
  disableExpand = false,
}) => {
  const isHtmlContentEmpty = (html: string) => {
    if (!html) return true;
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return !doc.body.textContent?.trim();
  };

  return (
    <div className="rounded-base border bg-white flex flex-col m-3 px-4 py-2 min-h-[100px] max-h-[150px]">
      <div className="flex items-center justify-between pb-1">
        <span className="font-medium text-lg -tracking-[2.2%] text-[#001926]">
          Diagnosis
        </span>
        <IconButton
          onClick={onExpand}
          size="small"
          disabled={disableExpand}
          sx={{
            '&.Mui-disabled': {
              color: 'gray',
              opacity: 0.5,
            },
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          <ExpandNoteIcon />
        </IconButton>
      </div>
      <Box className="overflow-y-auto flex-1">
        {Object.keys(diagnosisRecords).length > 0 ? (
          Object.keys(diagnosisRecords).map((date) => {
            const validRecords = diagnosisRecords[date]?.filter(
              (record) => !isHtmlContentEmpty(record.content)
            );

            if (!validRecords || validRecords.length === 0) return null;

            return (
              <div key={date} className="flex flex-row items-start">
                <div className="flex-grow max-w-[calc(100%-100px)]">
                  {validRecords.map((record) => (
                    <Box key={`${record.record_id}-${record.content}`}>
                      <EditableText
                        defaultValue={record.content}
                        editable={false}
                        emptyPlaceholder=""
                      />
                    </Box>
                  ))}
                </div>
                <div className="mx-2 w-[100px] text-right">
                  <StyledTypography variant="caption">
                    {formatDate(validRecords[0].timestamp)}
                  </StyledTypography>
                </div>
              </div>
            );
          })
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            No diagnosis records found
          </div>
        )}
      </Box>
    </div>
  );
};

export default DiagnosisCard;
