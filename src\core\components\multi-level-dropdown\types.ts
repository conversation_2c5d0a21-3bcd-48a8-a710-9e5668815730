import { FieldError } from 'react-hook-form';

import { IconButtonProps } from '@mui/material/IconButton';

export interface DropdownMenuOption {
  value: string;
  key?: string;
  subOptions?: DropdownMenuOption[] | string[];
}

export interface MultiDropDownProps {
  options: DropdownMenuOption[];
  onSelect?: (selectedOption: string, key: string) => void;
  value?: string;
  name?: string;
  error?: FieldError;
  label?: string;
  placeholder?: string;
  maxHeight?: string;
  disabledInput?: boolean;
  endDecoration?: React.ReactNode;
  className?: string;
  isMultiLevel?: boolean;
  isLoading?: boolean;
  loading?: boolean;
  isClearable?: boolean;
  onClear?: () => void;
  fieldClass?: string;
}

export type DropDownProps = {
  open: boolean;
  handleSelect: (value: string, key?: string, isSubOption?: boolean) => void;
  maxHeight?: string | number;
  isLoading?: boolean;
  options: DropdownMenuOption[];
  isMultiLevel?: boolean;
  loading?: boolean;
  value?: string;
};

export type DropDownItemsProps = {
  value: string;
  hasSubOption?: boolean;
  selected?: boolean;
  lastItem?: boolean;
  onClick?: () => void;
  isLoading?: boolean;
};

export type TopNavigationProps = {
  onClickBack: IconButtonProps['onClick'];
  onClose: IconButtonProps['onClick'];
};
