import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import ModalWrapper from '@/views/emr/lifestyle/shared/ModalWrapper';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

const ExercisePatternModal: React.FC<{
  patientData?: QuestionnaireResponse | null;
  mode?: LifestyleMode;
  onAfterSubmit?: () => void;
}> = ({ patientData, mode = LifestyleMode.CREATE, onAfterSubmit }) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = exercisePatternStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit } = methods;

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
            },
          };
          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting exercise pattern:', error);
      }
    },
    [
      setModalOpen,
      updateLifestyleData,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.id,
      createLifestyleData,
      onAfterSubmit,
    ]
  );

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, questions, methods]);

  return (
    <FormProvider {...methods}>
      <ModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
      >
        <ExercisePatternForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          showHeading={true}
        />
      </ModalWrapper>
    </FormProvider>
  );
};

export default memo(ExercisePatternModal);
