import React, { FC, memo, useCallback } from 'react';

import { AiOutlineLoading } from 'react-icons/ai';

import {
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';

import { DropdownMenuOption, DropDownProps } from './types';

const hoverHighlightClass =
  'hover:bg-[#A2DAF8] focus:bg-bg-[#A2DAF8] data-[highlighted]:bg-[#A2DAF8] data-[state=open]:bg-[#A2DAF8]';

const DropDown: FC<DropDownProps> = ({
  handleSelect,
  open,
  maxHeight,
  isLoading,
  options,
  isMultiLevel,
  loading,
}) => {
  const renderSubOptions = useCallback(
    (subOptions: (DropdownMenuOption | string)[]) => {
      return subOptions.map((subOption, idx) => {
        const hasNestedSubOptions =
          typeof subOption !== 'string' &&
          Array.isArray(subOption.subOptions) &&
          subOption.subOptions.length > 0;

        return (
          <React.Fragment key={idx}>
            {typeof subOption === 'string' || !hasNestedSubOptions ? (
              <DropdownMenuItem
                onClick={() =>
                  handleSelect(
                    typeof subOption === 'string' ? subOption : subOption.value,
                    (subOption as DropdownMenuOption).key as string,
                    true
                  )
                }
                className={`p-1 ${hoverHighlightClass}`}
              >
                {typeof subOption === 'string' ? subOption : subOption.value}
              </DropdownMenuItem>
            ) : (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger
                  className={`p-1 ${hoverHighlightClass}`}
                  onClick={() =>
                    handleSelect(subOption.value, subOption.key as string)
                  }
                >
                  {subOption.value}
                </DropdownMenuSubTrigger>
                <DropdownMenuSubContent
                  className={`ml-2 border-black overflow-y-auto rounded-thin-scrollbar ${maxHeight}`}
                >
                  {renderSubOptions(subOption.subOptions || [])}
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            )}
            {idx < subOptions.length - 1 && (
              <DropdownMenuSeparator className="bg-[#637D92] h-[0.5px] mx-[0.5px]" />
            )}
          </React.Fragment>
        );
      });
    },
    [handleSelect, maxHeight]
  );

  if (!open) return null;

  return (
    <DropdownMenuContent
      className={`border border-black overflow-y-auto rounded-thin-scrollbar ${maxHeight} z-[9999] bg-white`}
      side="bottom"
      align="start"
      forceMount
    >
      {isLoading ? (
        <div className="p-2 flex justify-center text-center">
          <AiOutlineLoading className="animate-spin" />
        </div>
      ) : (
        <>
          {options.map((option, idx) => (
            <React.Fragment key={idx}>
              {(option.subOptions && option.subOptions.length > 0) ||
              isMultiLevel ? (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger
                    className={`p-1 ${hoverHighlightClass}`}
                    onClick={() => handleSelect(option.value)}
                    onMouseEnter={() =>
                      handleSelect(option.value, option.key as string)
                    }
                  >
                    {option.value}
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent
                    className={`ml-3.5 border-black overflow-y-auto rounded-thin-scrollbar ${maxHeight}`}
                  >
                    {loading ? (
                      <div className="p-2 flex justify-center text-center">
                        <AiOutlineLoading className="animate-spin" />
                      </div>
                    ) : (
                      renderSubOptions(option?.subOptions ?? [])
                    )}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              ) : (
                <DropdownMenuItem
                  className={`p-1 ${hoverHighlightClass}`}
                  onClick={() =>
                    handleSelect(option.value, option.key as string)
                  }
                >
                  {option.value}
                </DropdownMenuItem>
              )}
              {idx < options.length - 1 && (
                <DropdownMenuSeparator className="bg-[#637D92] h-[0.5px] mx-[0.5px]" />
              )}
            </React.Fragment>
          ))}
        </>
      )}
    </DropdownMenuContent>
  );
};

export default memo(DropDown);
