import { create } from 'zustand';

import {
  createExtraNoteDiagnosis,
  getExtraNoteDiagnosis,
  updateExtraNoteDiagnosis,
} from '@/query/consultation/extraNoteSection';

import { ModeType, noteModes } from '@/utils/constants/consultation';

export interface DiagnosisRecord {
  record_id: string;
  field: string | null;
  content: string;
  doctor_id: string | undefined;
  timestamp: string;
  version: number;
  status: string;
}

export interface DiagnosisRecords {
  id?: string;
  records?: DiagnosisRecord[];
  created_on?: string;
}

interface DiagnosisState {
  records: DiagnosisRecords | null;
  loading: boolean;
  error: string | null;
  mode: ModeType;
  selectedRecord: DiagnosisRecord | null;

  getExtraNotes: (_patientId: string) => Promise<void>;
  addExtraNote: (
    _patientId: string,
    _data: Omit<DiagnosisRecords, 'id' | 'created_on'>
  ) => Promise<void>;
  updateExtraNote: (
    _id: string,
    _data: Partial<DiagnosisRecords>
  ) => Promise<void>;
  setMode: (_mode: ModeType) => void;
  setSelectedRecord: (_record: DiagnosisRecord | null) => void;
}

export const useDiagnosisStore = create<DiagnosisState>((set) => ({
  records: null,
  loading: false,
  error: null,
  mode: noteModes.NONE,
  selectedRecord: null,

  getExtraNotes: async (patientId) => {
    try {
      const response = await getExtraNoteDiagnosis(patientId);
      const latestRecord: DiagnosisRecords = response.data.reduce(
        (latest: DiagnosisRecords, item: DiagnosisRecords) =>
          new Date(item.created_on as string) >
          new Date(latest.created_on as string)
            ? item
            : latest,
        response.data[0]
      );

      set({ records: { ...latestRecord } });
    } catch (error: any) {
      set({ error: error.message });
    }
  },

  addExtraNote: async (patientId, data) => {
    set({ loading: true, error: null });
    try {
      await createExtraNoteDiagnosis(data, patientId);
      set({ loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  updateExtraNote: async (id, data) => {
    set({ loading: true, error: null });

    try {
      await updateExtraNoteDiagnosis(id, data);

      set((state) => {
        if (!state.records) return { loading: false };

        return {
          loading: false,
        };
      });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  setMode: (mode) => set({ mode }),
  setSelectedRecord: (record) => set({ selectedRecord: record }),
}));
