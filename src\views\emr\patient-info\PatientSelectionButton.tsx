import React, { memo } from 'react';

import { ButtonBase, ButtonBaseProps } from '@mui/material';
import { BiCheckCircle } from 'react-icons/bi';

import { cn } from '@/lib/utils';

import colors from '@/utils/colors';

type PatientSelectionButtonProps = React.HTMLProps<HTMLDivElement> & {
  isActive?: boolean;
  onClick: ButtonBaseProps['onClick'];
};

const PatientSelectionButton = ({
  isActive,
  onClick,
  ...rest
}: PatientSelectionButtonProps) => {
  return (
    <div
      {...rest}
      className={cn(
        'text-white rounded-lg min-w-[135px] max-h-9',
        'text-[16px] font-normal whitespace-nowrap',
        'flex items-center justify-center flex-grow',
        {
          [`bg-[${colors.common.aquaGreen}]`]: isActive,
          [`bg-black`]: !isActive,
        }
      )}
    >
      <ButtonBase
        onClick={onClick}
        disabled={isActive}
        className="!py-2 !px-2 w-full"
      >
        {isActive ? (
          <>
            <BiCheckCircle className="shrink-0" />
            &nbsp;
            <span>Currently Active</span>
          </>
        ) : (
          'Start Consultation'
        )}
      </ButtonBase>
    </div>
  );
};

export default memo(PatientSelectionButton);
