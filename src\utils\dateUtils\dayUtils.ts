import dayjs, { ConfigType, Dayjs } from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { convertToTimeObject } from '@/core/components/app-time-picker/utils';

import { DateFormats } from './dateFormats';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

export type DateType = Dayjs | null | ConfigType;
export type DateRangeType = 'week' | 'month' | 'year';

const { DATE_DD_MM_YYYY } = DateFormats;

export const parseDate = (date?: DateType | string): Dayjs | null => {
  if (date) {
    const parsedDate = dayjs(date);
    return parsedDate.isValid() ? parsedDate : null;
  }

  return null;
};

export const convertISTtoUTC = (
  date?: DateType | string,
  format: string = 'YYYY-MM-DDTHH:mm:ss[Z]'
): string => {
  if (!date) return '';

  const istDate = dayjs.tz(date, 'Asia/Kolkata');
  return istDate.isValid() ? istDate.utc().format(format) : '';
};

export const formatDate = (
  date?: DateType | string,
  format: string = DATE_DD_MM_YYYY
): string => {
  const parsedDate = parseDate(date);
  return parsedDate ? parsedDate.format(format) : '';
};

export const getDateBoundary = (range: DateRangeType = 'month') => {
  return {
    start: (date: DateType = dayjs()) => dayjs(date).startOf(range),
    end: (date: DateType = dayjs()) => dayjs(date).endOf(range),
  };
};

export const createDateRangeBoundaryNavigator = (
  range: DateRangeType = 'month'
) => {
  const shiftDateByRange = (
    date: DateType = dayjs(),
    direction: 'next' | 'previous'
  ) => {
    const adjustmentMethod = direction === 'next' ? 'add' : 'subtract';
    return dayjs(date)[adjustmentMethod](1, range);
  };

  return {
    next: (date: DateType = dayjs()) => shiftDateByRange(date, 'next'),
    previous: (date: DateType = dayjs()) => shiftDateByRange(date, 'previous'),
  };
};

export const isToday = (date?: string) => {
  const parsedDate = parseDate(date);
  if (!parsedDate) false;
  return dayjs(parsedDate).isSame(dayjs(), 'day');
};

export const getTimeAgo = (date: string): string => {
  return dayjs(date).fromNow();
};

export const addDateAndTime = (date: string, time: string): string => {
  const timeObj = convertToTimeObject(time);

  let hour = parseInt(timeObj.hour, 10);
  const minute = parseInt(timeObj.minute, 10);

  if (timeObj.period === 'PM' && hour < 12) {
    hour += 12;
  } else if (timeObj.period === 'AM' && hour === 12) {
    hour = 0;
  }

  const newDate = dayjs(date)
    .set('hour', hour)
    .set('minute', minute)
    .set('second', 0)
    .set('millisecond', 0);

  return newDate.toISOString();
};
