import { getOrDefault } from '@/utils/common';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import PrintButton from '@/views/emr/lab/print-button';
import StatusIndicator, {
  getBgColor,
} from '@/views/emr/lab/test-result/StatusIndicator';

import { Row } from '@/core/components/table/types';
import { TestResultItem } from '@/types/emr/lab';

const { DATE_DD_MM_YYYY_SLASH } = DateFormats;

const getTableBgColor = (i: number) => {
  return i % 2 === 0 ? '#C2CDD6 !important' : '#FFFFFF';
};

const getDummyRow = (i: number) => ({
  value: undefined,
  returnNullForEmpty: true,
  cellProps: { sx: { bgcolor: getTableBgColor(i) } },
});

type GetLabTestsTableRowsProps = {
  testResult: TestResultItem[];
  fetchTestResult?: () => void;
};

export const getLabTestsTableRows = ({
  testResult,
  fetchTestResult,
}: GetLabTestsTableRowsProps): Row[] => {
  if (!testResult?.length) return [];

  const rows: Row[] = [];

  testResult.forEach((item, i) => {
    const departments = Object.entries(item.labTests);
    const allTests = departments.flatMap(([_, tests]) => tests);
    const totalTests = allTests.length;

    const hasUniformStatus = allTests.every(
      (test) => test.status === allTests[0].status
    );

    let testRowIndex = 0;

    departments.forEach(([department, tests]) => {
      tests.forEach((test, j) => {
        const isFirstTest = testRowIndex === 0;
        const isFirstInDept = j === 0;

        const row: Row = {
          date: isFirstTest
            ? {
                value: formatDate(item?.created_on, DATE_DD_MM_YYYY_SLASH),
                cellProps: {
                  rowSpan: totalTests,
                  sx: { bgcolor: getTableBgColor(i) },
                },
              }
            : getDummyRow(i),

          department: isFirstInDept
            ? {
                value: department,
                cellProps: {
                  rowSpan: tests.length,
                  sx: { bgcolor: getTableBgColor(i) },
                },
              }
            : getDummyRow(i),

          status: hasUniformStatus
            ? isFirstTest
              ? {
                  value: (
                    <StatusIndicator
                      status={allTests[0].status}
                      id={item.id}
                      fetchTestResult={fetchTestResult}
                    />
                  ),
                  cellProps: {
                    rowSpan: totalTests,
                    sx: { bgcolor: getBgColor(allTests[0].status) },
                  },
                }
              : getDummyRow(i)
            : {
                value: (
                  <StatusIndicator
                    status={allTests[0].status}
                    id={item.id}
                    fetchTestResult={fetchTestResult}
                  />
                ),
                cellProps: {
                  sx: { bgcolor: getBgColor(allTests[0].status) },
                },
              },

          expand: hasUniformStatus
            ? isFirstTest
              ? {
                  value: (
                    <PrintButton
                      disabled={allTests[0].status !== 'Uploaded'}
                      testItem={item}
                    />
                  ),
                  cellProps: {
                    rowSpan: totalTests,
                    sx: { bgcolor: getTableBgColor(i) },
                  },
                }
              : getDummyRow(i)
            : {
                value: (
                  <PrintButton
                    disabled={allTests[0].status !== 'Uploaded'}
                    testItem={item}
                  />
                ),
                cellProps: {
                  sx: { bgcolor: getTableBgColor(i) },
                },
              },

          testName: {
            value: getOrDefault(test.testName, '-'),
            cellProps: { sx: { bgcolor: getTableBgColor(i), width: 90 } },
          },
          reference: {
            value: getOrDefault(test.reference, '-'),
            cellProps: { sx: { bgcolor: getTableBgColor(i) } },
          },
          result: {
            value: getOrDefault(test.results, '-'),
            cellProps: { sx: { bgcolor: getTableBgColor(i) } },
          },
        };

        rows.push(row);
        testRowIndex++;
      });
    });
  });

  return rows;
};

export const getRecentTestsFiltered = (
  tests: TestResultItem[]
): TestResultItem[] => {
  const filteredTests: TestResultItem[] = tests.filter((test) => {
    const tests = Object.entries(test.labTests).flatMap(([_, tests]) =>
      tests.filter(
        (test) => test.fileMetadata?.length > 0 && test.status === 'Uploaded'
      )
    );
    return tests.length > 0;
  });

  return filteredTests;
};
