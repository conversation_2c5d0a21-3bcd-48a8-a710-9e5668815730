import { toast } from 'sonner';
import { create } from 'zustand';

import { modal } from '@/hooks/useModal';

import {
  createMedicinePackage,
  fetchMedicinePackages,
  fetchMedicinesByPackageId,
  updateMedicinePackage,
  deleteMedicinePackage,
} from '@/query/emr/prescription/prescription-package';

import { getErrorMessage } from '@/utils/error-message';

import {
  PrescriptionModalMode,
  prescriptionModalModes,
} from '@/types/emr/prescription';
import {
  MedicineItem,
  PrescriptionPackageData,
} from '@/types/emr/prescription/package';
import {
  initialState,
  PrescriptionPackageStore,
} from '@/types/emr/prescription/package-store';

import { usePrescriptionStore } from '.';

export const usePrescriptionPackageStore = create<PrescriptionPackageStore>(
  (set, get) => ({
    ...initialState,

    openModal: (packageType) =>
      set({
        isModalOpen: true,
        activePackageType: packageType,
        modalMode: prescriptionModalModes.VIEW,
        selectedPackage: null,
        medicineItems: [],
        hasUnsavedChanges: false,
        showUnsavedChangesModal: false,
        showDeleteConfirmationModal: false,
        originalPackageName: '',
        originalMedicineItems: [],
        newPackageName: '',
        editingPackageName: '',
      }),

    closeModal: () => {
      usePrescriptionStore.getState().clearPackageMedicines();
      set({
        isModalOpen: false,
        modalMode: prescriptionModalModes.VIEW,
        selectedPackage: null,
        medicineItems: [],
        newPackageName: '',
        editingPackageName: '',
        activePackageType: null,
        hasUnsavedChanges: false,
        showUnsavedChangesModal: false,
        showDeleteConfirmationModal: false,
        originalPackageName: '',
        originalMedicineItems: [],
      });
    },

    setModalMode: (mode) => {
      const { selectedPackage, editingPackageName, medicineItems } = get();

      if (mode === prescriptionModalModes.CREATE && selectedPackage) {
        set({
          modalMode: mode,
          originalPackageName: editingPackageName,
          originalMedicineItems: [...medicineItems],
        });
      } else {
        set({ modalMode: mode });
      }
    },

    setSelectedPackage: async (pkg) => {
      set({
        selectedPackage: pkg,
        modalMode: prescriptionModalModes.DETAIL,
        medicineItems: [],
        editingPackageName: pkg.name,
        originalPackageName: pkg.name,
        isLoading: true,
      });

      try {
        const response = await fetchMedicinesByPackageId(pkg.id);
        const medicines: MedicineItem[] = response.data.medicines.map(
          (medicine) => ({
            ...medicine,
            id: medicine.id,
            medicineName: medicine.medicineName,
            brandName: medicine.brandName || '',
            strength: medicine.strength || '',
            drugForm: medicine.drugForm || '',
            selected: false,
          })
        );

        set({
          medicineItems: medicines,
          originalMedicineItems: [...medicines],
        });
      } catch (error) {
        toast.error('Failed to load medicines');
        console.error('Fetch medicines error:', error);
      } finally {
        set({ isLoading: false });
      }
    },

    setPackageName: (name) => {
      const { selectedPackage } = get();
      if (selectedPackage) {
        set({ editingPackageName: name });
      } else {
        set({ newPackageName: name });
      }
    },

    addMultipleMedicines: (medicines) =>
      set((state) => {
        const existingIds = new Set(state.medicineItems.map((item) => item.id));
        const newMedicines = medicines
          .filter((medicine) => !existingIds.has(medicine.id))
          .map((medicine) => ({
            ...medicine,
            id: medicine.id,
            medicineName: medicine.medicineName,
            brandName: medicine.brandName || '',
            strength: medicine.strength || '',
            drugForm: medicine.drugForm || '',
            selected: false,
          }));

        return {
          medicineItems: [...state.medicineItems, ...newMedicines],
        };
      }),

    removeMedicine: (medicineId) =>
      set((state) => ({
        medicineItems: state.medicineItems.filter(
          (item) => item.id !== medicineId
        ),
      })),

    toggleMedicineSelection: (medicineId) =>
      set((state) => ({
        medicineItems: state.medicineItems.map((item) =>
          item.id === medicineId ? { ...item, selected: !item.selected } : item
        ),
      })),

    clearAllMedicines: () => {
      const { medicineItems } = get();

      const formattedMedicines = medicineItems.map((item) => ({
        ...item,
        selected: false,
      }));

      set({ medicineItems: formattedMedicines });
    },

    selectAllMedicines: () => {
      const { medicineItems } = get();

      const formattedMedicines = medicineItems.map((item) => ({
        ...item,
        selected: true,
      }));

      set({ medicineItems: formattedMedicines });
    },

    startCreatePackage: (mode: PrescriptionModalMode) => {
      const selectedMedicines =
        usePrescriptionStore.getState().selectedMedicines;

      const mappedMedicines: MedicineItem[] = selectedMedicines.map(
        (medicine) => ({
          ...medicine,
          id: medicine.id as string,
          medicineName: medicine.GenericName as string,
          brandName: medicine.BrandName || '',
          strength: medicine.Strength || '',
          drugForm: medicine.DrugFormulation || '',
          selected: false,
          unitOfMeasure: medicine.UnitOfMeasure || '',
          measure: medicine.Measure || '',
          cost: parseFloat(medicine.cost as string) || 0,
        })
      );

      set({
        modalMode: mode,
        selectedPackage: null,
        medicineItems:
          mode === prescriptionModalModes.ADD ? mappedMedicines : [],
        newPackageName: '',
        originalPackageName: '',
        originalMedicineItems: [],
      });
    },

    addSelectedPackageToMedicines: () => {
      const { medicineItems } = get();
      const selectedMedicines = medicineItems.filter((item) => item.selected);

      if (selectedMedicines.length === 0) {
        toast.error('No medicines selected');
        return;
      }

      selectedMedicines.forEach((medicine) => {
        const medicineToAdd = {
          id: String(medicine.id),
          GenericName: medicine.medicineName,
          BrandName: medicine.brandName,
          Strength: medicine.strength,
          DrugFormulation: medicine.drugForm,
          UnitOfMeasure: medicine.unitOfMeasure,
          Measure: medicine.measure,
          Cost: String(medicine.cost),
        };
        usePrescriptionStore.getState().addSelectedMedicine(medicineToAdd);
      });

      set({
        isModalOpen: false,
        activePackageType: null,
        medicineItems: medicineItems.map((item) => ({
          ...item,
          selected: false,
        })),
      });
    },

    setShowUnsavedChangesModal: (show) =>
      set({ showUnsavedChangesModal: show }),

    checkUnsavedChanges: () => {
      const {
        modalMode,
        newPackageName,
        editingPackageName,
        originalPackageName,
        medicineItems,
        originalMedicineItems,
        selectedPackage,
      } = get();

      if (modalMode === prescriptionModalModes.CREATE) {
        if (selectedPackage) {
          const nameChanged = editingPackageName !== originalPackageName;
          const medicinesChanged =
            medicineItems.length !== originalMedicineItems.length ||
            medicineItems.some((item, index) => {
              const originalItem = originalMedicineItems[index];
              return (
                !originalItem ||
                item.id !== originalItem.id ||
                item.medicineName !== originalItem.medicineName ||
                item.selected !== originalItem.selected
              );
            });

          return nameChanged || medicinesChanged;
        } else {
          const result =
            newPackageName.trim() !== '' || medicineItems.length > 0;
          return result;
        }
      } else if (
        modalMode === prescriptionModalModes.DETAIL &&
        selectedPackage
      ) {
        const medicinesChanged = medicineItems.some((item, index) => {
          const originalItem = originalMedicineItems[index];
          return !originalItem || item.selected !== originalItem.selected;
        });

        return medicinesChanged;
      }

      return false;
    },

    handleCancelWithCheck: () => {
      const { checkUnsavedChanges, setModalMode, modalMode, selectedPackage } =
        get();
      const hasChanges = checkUnsavedChanges();

      if (hasChanges) {
        set({ showUnsavedChangesModal: true });
      } else if (
        modalMode === prescriptionModalModes.CREATE &&
        selectedPackage
      ) {
        setModalMode(prescriptionModalModes.DETAIL);
      } else {
        setModalMode(prescriptionModalModes.VIEW);
      }
    },

    resetUnsavedChanges: () => {
      set({
        hasUnsavedChanges: false,
        showUnsavedChangesModal: false,
        originalPackageName: '',
        originalMedicineItems: [],
      });
    },

    setShowDeleteConfirmationModal: (show) =>
      set({ showDeleteConfirmationModal: show }),

    addNewMedicineToPackage: () => {
      const { startCreatePackage, checkUnsavedChanges, handleCancelWithCheck } =
        get();
      if (checkUnsavedChanges()) {
        handleCancelWithCheck();
      } else {
        startCreatePackage(prescriptionModalModes.CREATE);
      }
    },

    createNewPackage: async () => {
      const {
        newPackageName,
        medicineItems,
        activePackageType,
        fetchPackages,
        packages,
      } = get();

      if (!newPackageName.trim()) {
        toast.error('Package name is required');
        return;
      }

      if (medicineItems.length === 0) {
        toast.error('At least one medicine must be added');
        return;
      }

      const isDuplicate = packages.some(
        (pkg) =>
          pkg.name.toLowerCase().trim() ===
            newPackageName.toLowerCase().trim() &&
          pkg.type === activePackageType
      );

      if (isDuplicate) {
        toast.error('A package with this name already exists');
        return;
      }

      set({ isCreating: true });

      try {
        const medicineIds = medicineItems.map((item) => item.id);
        if (!activePackageType) {
          toast.error('Package type is not selected');
          return;
        }
        await createMedicinePackage({
          name: newPackageName.trim(),
          type: activePackageType,
          medicineIds,
        });

        modal.success('New package created successfully!');
        await fetchPackages();
        set({
          newPackageName: '',
          medicineItems: [],
          modalMode: prescriptionModalModes.VIEW,
          // Reset unsaved changes
          hasUnsavedChanges: false,
          showUnsavedChangesModal: false,
          originalPackageName: '',
          originalMedicineItems: [],
        });
      } catch (error) {
        const errorMessage = getErrorMessage(error, 'Failed to create package');
        toast.error(errorMessage);
        console.error('Create package error:', error);
      } finally {
        set({ isCreating: false });
      }
    },

    updateExistingPackage: async () => {
      const {
        selectedPackage,
        medicineItems,
        editingPackageName,
        packages,
        activePackageType,
        fetchPackages,
        originalPackageName,
      } = get();

      if (!selectedPackage) {
        toast.error('No package selected');
        return;
      }

      if (medicineItems.length === 0) {
        toast.error('At least one medicine must be added');
        return;
      }

      const isNameChanged = editingPackageName.trim() !== selectedPackage.name;
      if (isNameChanged) {
        if (!editingPackageName.trim()) {
          toast.error('Package name cannot be empty');
          return;
        }

        const isDuplicate = packages.some(
          (pkg) =>
            pkg.id !== selectedPackage.id &&
            pkg.name.toLowerCase().trim() ===
              editingPackageName.toLowerCase().trim() &&
            pkg.type === activePackageType
        );

        if (isDuplicate) {
          toast.error('A package with this name already exists');
          return;
        }
      }

      set({ isCreating: true });

      try {
        const medicineIds = medicineItems.map((item) => item.id);
        const updateData: { medicineIds: (string | number)[]; name?: string } =
          {
            medicineIds,
          };

        if (isNameChanged) {
          updateData.name = editingPackageName.trim();
        }

        await updateMedicinePackage(selectedPackage.id as string, updateData);

        modal.success('Package saved successfully!');

        if (isNameChanged) {
          set({
            selectedPackage: {
              ...selectedPackage,
              name: editingPackageName.trim(),
            },
          });
          await fetchPackages();
        }

        set({
          modalMode: prescriptionModalModes.DETAIL,
          // Reset unsaved changes and update original values
          hasUnsavedChanges: false,
          showUnsavedChangesModal: false,
          originalPackageName: isNameChanged
            ? editingPackageName.trim()
            : originalPackageName,
          originalMedicineItems: [...medicineItems],
        });
      } catch (error) {
        const errorMessage = getErrorMessage(error, 'Failed to update package');
        toast.error(errorMessage);
        console.error('Update package error:', error);
      } finally {
        set({ isCreating: false });
      }
    },

    fetchPackages: async () => {
      const { activePackageType } = get();

      if (!activePackageType) {
        toast.error('Package type is not selected');
        return;
      }

      set({ isPackagesLoading: true });

      try {
        const response = await fetchMedicinePackages(activePackageType);

        const packages: PrescriptionPackageData[] = response.data.map(
          (pkg) => ({
            id: pkg.id,
            name: pkg.name,
            type: activePackageType,
            medicines: [],
          })
        );

        set({ packages });
      } catch (error) {
        toast.error('Failed to fetch packages');
        console.error('Fetch packages error:', error);
      } finally {
        set({ isPackagesLoading: false });
      }
    },

    deletePackage: async () => {
      const { selectedPackage, fetchPackages } = get();

      if (!selectedPackage) {
        toast.error('No package selected for deletion');
        return;
      }

      set({ isDeleting: true });

      try {
        await deleteMedicinePackage(selectedPackage.id);
        modal.success('Package deleted successfully');

        await fetchPackages();

        set({
          modalMode: prescriptionModalModes.VIEW,
          selectedPackage: null,
          medicineItems: [],
          newPackageName: '',
          editingPackageName: '',
          hasUnsavedChanges: false,
          showUnsavedChangesModal: false,
          showDeleteConfirmationModal: false,
          originalPackageName: '',
          originalMedicineItems: [],
        });
      } catch (error) {
        toast.error('Failed to delete package');
        console.error('Delete package error:', error);
      } finally {
        set({ isDeleting: false });
      }
    },
  })
);
