'use client';

import React, { memo, useCallback } from 'react';

import { FaPlus } from 'react-icons/fa';

import Link from 'next/link';

import { useMrdPatientSearch } from '@/store/mrd/manage-patient/patient-search';

import { routes } from '@/constants/routes';

import AppButton from '@/core/components/app-button';
import Loader from '@/core/components/app-loaders/Loader';
import AppTitle from '@/core/components/app-title';

import AsyncSearch from './async-search';
import PatientDetails from './patient-details';

const { MRD_MANAGE_PATIENTS_MANAGE } = routes;

const PatientSearch = () => {
  const { loading, patient } = useMrdPatientSearch();

  const renderBodyContent = useCallback(() => {
    if (loading) {
      return <Loader />;
    }
    if (patient) {
      return <PatientDetails />;
    }
    return (
      <div className="flex items-end justify-center w-full h-full">
        <AppButton
          fullWidth
          startIcon={<FaPlus className="!text-sm !font-normal" />}
          size="large"
          component={Link}
          href={MRD_MANAGE_PATIENTS_MANAGE}
        >
          Register New Patient
        </AppButton>
      </div>
    );
  }, [loading, patient]);

  return (
    <div className="w-full h-full">
      <div className="flex flex-col gap-base w-full h-20">
        <AppTitle>Manage Patients</AppTitle>
        <AsyncSearch />
      </div>
      <div className="flex-1 w-full h-[calc(100%-5rem)]">
        {renderBodyContent()}
      </div>
    </div>
  );
};

export default memo(PatientSearch);
