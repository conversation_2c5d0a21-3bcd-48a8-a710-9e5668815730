{"cSpell.words": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "abha", "addictionhistory", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Apim", "appt", "arca", "archivo", "bgcolor", "<PERSON><PERSON><PERSON>", "blocknote", "<PERSON><PERSON><PERSON>", "cognitiveservices", "continuetoken", "currentmedicationhistory", "customise", "diethistory", "eastus", "extralight", "familyhistory", "Finalise", "Finalised", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalphysicalexamination", "HDFC", "heent", "historyofpresenting", "ICICI", "Iconamoon", "Intolerances", "Macronutrients", "<PERSON><PERSON><PERSON>", "Micronutrients", "<PERSON><PERSON>", "msal", "<PERSON><PERSON>", "Pancard", "partialize", "pastmedicalhistory", "pastsurgicalhistory", "physicalactivityhistory", "Pincode", "postcoordination", "<PERSON><PERSON><PERSON>t", "presentingcomplaints", "qlementine", "Religare", "rheumatological", "Selectfield", "sleephistory", "S<PERSON>med", "sonner", "speechsdk", "Stagewise", "stresshistory", "subparameters", "systemicexamination", "Updation", "webchat"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"]}