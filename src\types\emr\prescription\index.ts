export type PrescriptionItem = {
  id?: string;
  drugForm?: string;
  brandName?: string;
  genericName?: string;
  strength?: string;
  measure?: string;
  uom?: string;
  nosPerMlOrGm?: string;
  frequency?: string;
  duration?: string;
  quantity?: string;
  route?: string;
  instructions?: string;
  cost?: string;
  canSubstitute?: boolean;
  close?: string;
  BrandName?: string;
  GenericName?: string;
  DrugFormulation?: string;
  Strength?: string;
  Measure?: string;
  UnitOfMeasure?: string;
  [key: string]: string | boolean | undefined;
};

export const defaultPrescriptionRow: PrescriptionItem = {
  id: '',
  drugForm: '',
  genericName: '',
  brandName: '',
  strength: '',
  measure: '',
  uom: '',
  nosPerMlOrGm: '',
  frequency: '',
  duration: '',
  quantity: '',
  route: '',
  instructions: '',
  cost: '',
  canSubstitute: false,
  close: '',
};

export type PrescriptionFormData = {
  prescription: PrescriptionItem[];
};

export const prescriptionTabs = {
  NEW_PRESCRIPTION: 'new_prescription',
  PRESCRIPTION_HISTORY: 'prescription_history',
} as const;

export type PrescriptionTabType =
  | (typeof prescriptionTabs)[keyof typeof prescriptionTabs]
  | string;

export const prescriptionModalTypes = {
  SAVE: 'save',
  CLEAR: 'clear',
} as const;

export type PrescriptionModalType =
  | (typeof prescriptionTabs)[keyof typeof prescriptionTabs]
  | string;

export const prescriptionHistoryView = {
  LIST: 'list',
  DETAILS: 'details',
} as const;

export const prescriptionMode = {
  NEW: 'new',
  EDIT: 'edit',
  VIEW: 'view',
  REFILL: 'refill',
} as const;

export type PrescriptionModeType =
  (typeof prescriptionMode)[keyof typeof prescriptionMode];

export type PrescriptionHistoryViewType =
  (typeof prescriptionHistoryView)[keyof typeof prescriptionHistoryView];

export type PrescriptionHistory = {
  doctor: string;
  date: string;
  id: string;
  updated_on: string;
  created_on: string;
  medicines: PrescriptionItem[];
};

export type PrescriptionUpdatePayload = {
  patientId?: string;
  doctor?: string;
  medicines: PrescriptionItem[];
};

export const prescriptionModalModes = {
  VIEW: 'view',
  CREATE: 'create',
  DETAIL: 'detail',
  ADD: 'add',
} as const;

export const prescriptionPackageTypes = {
  DEPARTMENT: 'department',
  USER: 'user',
} as const;

export type PrescriptionModalMode =
  (typeof prescriptionModalModes)[keyof typeof prescriptionModalModes];
export type PrescriptionPackageType =
  | (typeof prescriptionPackageTypes)[keyof typeof prescriptionPackageTypes]
  | null;
