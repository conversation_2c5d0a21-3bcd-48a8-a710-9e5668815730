import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createCustomiseEmr,
  getCustomiseEmr,
  getCustomiseEmrDropdownOptions,
  updateCustomiseEmr,
} from '@/query/emr/doctor-profile/customise-emr';

type SubParameter = {
  key: string;
  value: string;
  abbreviationCode?: string;
  unit?: string;
};
export type MultilevelDropdownType = {
  key: string;
  value: string;
  abbreviationCode?: string;
  unit?: string;
  subparameters?: SubParameter[];
};

type DropdownOptions = Record<string, { data: MultilevelDropdownType[] }[]>;
export type EmrTypes = {
  created_on?: string;
  tile_layout?: string | null;
  selected_tiles: {
    key: string;
    value: string;
    abbreviationCode?: string;
    unit?: string;
  }[][];
  medical_note_summary_template?: string;
  doc_assist_preference?: string;
  preferred_language_for_ambient_listening?: string;
  extraNote1?: string;
  extraNote2?: string;
  extraNote3?: string;
  id?: string;
} & Record<string, any>;
interface CustomiseEmrState {
  dropdownOptions: DropdownOptions;
  customiseEmrData: EmrTypes[] | null;
  isLoading: boolean;
  isDataLoading: boolean;
  errorMessage: string | null;
  fetchDropdownOptions: (_source: string) => Promise<void>;
  fetchCustomiseEmr: (_doctorId: string) => Promise<void>;
  createCustomEmrEntry: (_data: EmrTypes, _doctorId: string) => Promise<void>;
  updateCustomEmrEntry: (_id: string, _data: EmrTypes) => Promise<void>;
}

export const useCustomiseEmrStore = create<CustomiseEmrState>((set) => ({
  dropdownOptions: {},
  customiseEmrData: null,
  isLoading: false,
  errorMessage: null,
  isDataLoading: false,

  fetchDropdownOptions: async (source: string) => {
    set({ isLoading: true });
    try {
      const response = await getCustomiseEmrDropdownOptions(source);
      set((state) => ({
        isLoading: false,
        dropdownOptions: {
          ...state.dropdownOptions,
          [source]: response.data,
        },
      }));
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to fetch options';
      set({ isLoading: false, errorMessage });
      toast.error(errorMessage);
    }
  },

  createCustomEmrEntry: async (data, doctorId) => {
    set({ isDataLoading: true });

    try {
      await createCustomiseEmr(data, doctorId);
      toast.success(`Customise EMR created successfully`);
      set({ isDataLoading: false });
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create Customise EMR';
      set({ errorMessage });
      toast.error(errorMessage);
    }
  },

  fetchCustomiseEmr: async (doctorId: string) => {
    try {
      const response = await getCustomiseEmr(doctorId);
      set({ customiseEmrData: response.data });
    } catch (error: any) {
      const errorMessage =
        error.message || 'Failed to fetch Customise EMR data';
      set({ errorMessage });
      toast.error(errorMessage);
    }
  },

  updateCustomEmrEntry: async (id, data) => {
    set({ isDataLoading: true });

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 8000);

    try {
      const response = await updateCustomiseEmr(id, data, {
        signal: controller.signal,
      });
      toast.success(`Customise EMR updated successfully`);

      set({
        isDataLoading: false,
        customiseEmrData: response.data,
      });
    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.error('Request timed out. Please try again.');
      } else {
        const errorMessage =
          error.message || 'Failed to update Customise EMR data';
        toast.error(errorMessage);
      }
    } finally {
      clearTimeout(timeout);
      set({ isDataLoading: false });
    }
  },
}));
