import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import { calculateAge as calculateAgeByDob } from '@/helpers/dates';

import { Address } from '@/types/mrd/manage-patient/patient';

interface PatientCardProps {
  name?: string;
  dob?: string;
  sex?: string;
  address?: Address;
  calculateAge?: (dob: string) => number;
}

const PatientCard: React.FC<PatientCardProps> = ({
  name,
  dob,
  sex,
  address,
  calculateAge = (dob) => calculateAgeByDob(dob),
}) => {
  return (
    <div className="rounded-lg bg-white p-2 flex items-center gap-2.5 shadow-custom-xs border border-[#DAE1E7] min-w-0">
      <div className="rounded-full h-[40px] min-h-8 min-w-8 w-[40px] bg-[#E8EBED] border-2 border-red-700 flex items-center justify-center">
        {name && name[0]}
      </div>

      <div className="flex flex-col min-w-0 w-full">
        <span className="font-medium text-lg -tracking-[2.2%] break-words leading-snug">
          {name}
        </span>

        <div className="flex items-center gap-2.5 w-full min-w-0">
          {dob && (
            <span className="whitespace-nowrap text-sm flex-shrink-0">
              {`${calculateAge(dob)} yrs`}
            </span>
          )}
          <span className="h-4 w-[1px] bg-[#C2CDD6] flex-shrink-0"></span>

          <span className="text-sm flex-shrink-0">
            {sex && sex.charAt(0).toUpperCase()}
          </span>

          {address && (
            <>
              <span className="h-4 w-[1px] bg-[#C2CDD6] flex-shrink-0"></span>
              <span className="truncate overflow-hidden text-ellipsis text-sm min-w-0 flex-shrink">
                {formatAddress(address)}
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientCard;
