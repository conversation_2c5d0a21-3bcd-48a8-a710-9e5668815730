import { Address } from '@/types/mrd/manage-patient/patient';

type FormatAddressOptions = {
  fallback?: string;
  singleLine?: boolean;
};

export const formatAddress = (
  address?: Address,
  options?: FormatAddressOptions
): string => {
  const { fallback = '', singleLine = false } = options ?? {};
  if (!address) {
    return fallback;
  }
  if (typeof address === 'string') {
    return address;
  }
  let addressString = '';

  if (!singleLine) {
    if (address?.houseName) {
      addressString += `${address.houseName}, `;
    }
    if (address?.post) {
      addressString += `\n${address.post} P.O`;
    }
    if (address?.city) {
      addressString += `${address.city},  `;
    }
    if (address?.district) {
      addressString += `${address.district} Dist., `;
    }
    if (address?.state) {
      addressString += `\n${address.state}, `;
    }
    if (address?.pin) {
      addressString += `${address.pin}`;
    }
    if (address?.country) {
      addressString += `, ${address.country}`;
    }
  } else {
    if (address?.houseName) {
      addressString += `${address.houseName}, `;
    }
    if (address?.post) {
      addressString += `${address.post} P.O`;
    }
    if (address?.city) {
      addressString += `${address.city},  `;
    }
    if (address?.district) {
      addressString += `${address.district} Dist., `;
    }
    if (address?.state) {
      addressString += `${address.state}, `;
    }
    if (address?.pin) {
      addressString += `${address.pin}`;
    }
    if (address?.country) {
      addressString += `, ${address.country}`;
    }
  }

  return addressString;
};
