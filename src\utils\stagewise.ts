'use client';

import { initToolbar } from '@stagewise/toolbar';

const isBrowser = typeof window !== 'undefined';

export const stageWiseConfig = {
  stagewiseConfig: {
    plugins: [
      {
        name: 'arca-plugin',
        description: 'Adds additional context for your components',
        shortInfoForPrompt: () => {
          return 'Context information about the selected element';
        },
        mcp: null,
        actions: [],
      },
    ],
  },

  init: () => {
    if (!isBrowser) return;
    if (process.env.NEXT_PUBLIC_NODE_ENV === 'development') {
      initToolbar(stageWiseConfig.stagewiseConfig);
    }
  },
};
