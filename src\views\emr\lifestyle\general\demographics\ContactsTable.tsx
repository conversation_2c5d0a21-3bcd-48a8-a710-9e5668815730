import { useMemo } from 'react';

import { UseFieldArrayReturn } from 'react-hook-form';

import { InputBase } from '@mui/material';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { Contact } from '@/types/mrd/manage-patient/patient-details';

interface ContactsTableProps {
  contacts: Contact[];
  isViewMode?: boolean;
  fieldArray?: UseFieldArrayReturn<any, 'contacts', 'id'>;
  register?: any;
}

const tableHeaders: HeaderV2[] = [
  {
    key: 'phoneNumber',
    header: 'Phone Number',
    cellProps: { align: 'left' as const },
  },
  {
    key: 'email',
    header: 'Email',
    cellProps: { align: 'left' as const },
  },
];

const ContactsTable = ({
  contacts,
  isViewMode = false,
  fieldArray,
  register,
}: ContactsTableProps) => {
  const { fields, append, remove } = fieldArray || {};

  const headers = useMemo<HeaderV2[]>(() => {
    const headers: HeaderV2[] = tableHeaders;

    if (isViewMode) {
      return headers;
    } else {
      return [
        ...headers,
        {
          key: 'action',
          header: (
            <AppIconButton
              onClick={() => append?.({ phoneNumber: '', email: '' })}
              variant="outlined"
            >
              <AppIcon icon="mynaui:plus-solid" />
            </AppIconButton>
          ),
          cellProps: {
            align: 'center',
            sx: {
              border: 'none !important',
              backgroundColor: 'white !important',
              width: 60,
            },
          },
        },
      ];
    }
  }, [isViewMode, append]);

  const rows: RowV2[] = useMemo(() => {
    if (isViewMode) {
      return contacts.map((contact, index) => ({
        key: index.toString(),
        phoneNumber: {
          value: <div className="text-left">{contact?.phone || '-'}</div>,
          cellProps: { align: 'left', sx: { textAlign: 'left' } },
        },
        email: {
          value: <div className="text-left">{contact.email || '-'}</div>,
          cellProps: { align: 'left', sx: { textAlign: 'left' } },
        },
      }));
    }
    return (fields || []).map((field, index) => ({
      key: field.id,
      phoneNumber: {
        value: (
          <InputBase
            {...register(`contacts.${index}.phone`)}
            type="number"
            className="w-full border-none bg-transparent focus:outline-none"
            placeholder="+91 9876543210"
          />
        ),
        cellProps: { align: 'left' },
      },
      email: {
        value: (
          <InputBase
            {...register(`contacts.${index}.email`)}
            className="w-full border-none bg-transparent focus:outline-none"
            placeholder="<EMAIL>"
          />
        ),
        cellProps: { align: 'left' },
      },
      action: {
        value: (
          <AppIconButton onClick={() => remove?.(index)} variant="outlined">
            <AppIcon icon="tabler:minus" />
          </AppIconButton>
        ),
        cellProps: {
          align: 'left',
          sx: {
            border: 'none !important',
            width: 60,
          },
        },
      },
    }));
  }, [isViewMode, contacts, fields, register, remove]);

  return (
    <div className="flex items-start">
      <TableV2
        headers={headers}
        rows={rows}
        tableContainerProps={{
          sx: {
            '& .MuiTableCell-root': {
              padding: '12px 16px',
            },
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: '#F5F8FA',
              fontWeight: 500,
              fontSize: '0.875rem',
              color: '#6B7280',
            },
          },
        }}
      />
    </div>
  );
};

export default ContactsTable;
