import { useState, useCallback, useRef } from 'react';

import { components } from 'react-select';
import type { SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';

import { createDebouncedSearch } from '@/utils/search';

import AsyncSearch from '@/core/components/search';
import type { PrescriptionItem } from '@/types/emr/prescription';

interface MedicineOption {
  value: string;
  label: string;
  medicine: PrescriptionItem;
}

interface MedicineSearchProps {
  placeholder?: string;
  onChange?: (medicine: PrescriptionItem | null) => void;
}

const MedicineSearch: React.FC<MedicineSearchProps> = ({
  placeholder = 'Search by Medicine',
  onChange,
}) => {
  const { searchMedicines, selectMedicine } = usePrescriptionStore();
  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<MedicineOption | null>(
    null
  );

  const abortControllerRef = useRef<AbortController | null>(null);

  // Create debounced search instance
  const { search, cancel } = createDebouncedSearch<PrescriptionItem>(
    searchMedicines,
    300,
    {
      minLength: 1,
      onError: (error) => {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error searching medicines:', error);
        }
      },
    }
  );

  const loadOptions = useCallback(
    (
      inputValue: string,
      callback: (options: readonly MedicineOption[]) => void
    ) => {
      // Cancel the previous request if it exists
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create a new AbortController for the current request
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      search(
        inputValue,
        (medicines) => {
          if (signal?.aborted) return;

          const options: MedicineOption[] = medicines
            .filter((medicine) => !!medicine.id)
            .map((medicine) => ({
              value: medicine.id as string,
              label: medicine.BrandName || medicine.GenericName || '',
              medicine: { ...medicine, quantity: '1' },
            }));

          callback(options);
        },
        signal
      );
    },
    [search]
  );

  const handleChange = (newValue: SingleValue<MedicineOption>) => {
    const medicine = newValue ? newValue.medicine : null;
    if (medicine) {
      const medicineWithDefaults = {
        ...medicine,
        quantity: '1',
        cost: String(medicine.Cost || '0'),
      };
      selectMedicine(medicineWithDefaults);
      if (onChange) onChange(medicineWithDefaults);
    } else {
      selectMedicine(null);
      if (onChange) onChange(null);
    }

    setSelectedOption(newValue);
    setInputValue('');
    setTimeout(() => {
      setSelectedOption(null);
    }, 800);
  };

  const CustomOption = (props: any) => {
    const { medicine } = props.data;

    const medicineDetails = [
      { label: medicine.DrugFormulation, flex: 2 },
      { label: medicine.BrandName, flex: 4 },
      { label: medicine.Strength, flex: 4 },
      { label: medicine.Measure, flex: 1 },
      { label: medicine.UnitOfMeasure, flex: 1 },
    ];

    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0}>
            {medicineDetails.map((detail, index) => (
              <Box
                key={index}
                flex={detail.flex}
                textAlign="left"
                ml={index > 0 ? 1 : 0}
              >
                <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                  {detail.label}
                </Typography>
              </Box>
            ))}
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  return (
    <AsyncSearch<MedicineOption>
      loadOptions={loadOptions}
      onChange={handleChange}
      placeholder={placeholder}
      defaultOptions={[]}
      cacheOptions
      components={{ Option: CustomOption }}
      value={selectedOption}
      inputValue={inputValue}
      onInputChange={(newVal, { action }) => {
        if (action === 'input-change') {
          setInputValue(newVal);
        }
      }}
    />
  );
};

export default MedicineSearch;
