'use client';

import { memo, useState } from 'react';

import { useForm } from 'react-hook-form';

import AutoResizeTextArea from '@/views/emr/doctor-profile/personal-info/shared/AutoResizeTextArea';

import CustomRadioGroup from '@/components/ui/custom-radio-group';

import PrimaryButton from '@/core/components/primary-button';

import NicotineDependenceTest from './NicotineDependenceTest';

import MedicalHistoryAddictionViewComponent from './view/MedicalHistoryAddictionView';

type FormValues = {
  disease: string;
  treatmentHistory: string;
  yearOfDiagnosis: string;
  diagnosisDuration: string;
  durationUnit: string;
  status: 'active' | 'inactive';
  smokingHistory: 'no' | 'former' | 'current';
  smokingPackYears: string;
  smokingFrequency: string;
  alcoholConsumption: 'no' | 'former' | 'current';
  oralTobacco: 'no' | 'former' | 'current';
  drugs: 'no' | 'former' | 'current';
};

const MedicalHistoryAddictionView = () => {
  const [isViewMode, setIsViewMode] = useState(true);
  const [isNicotineTestOpen, setIsNicotineTestOpen] = useState(false);
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 100 }, (_, i) => currentYear - i);

  const {
    register,
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitted },
  } = useForm<FormValues>({
    defaultValues: {
      disease: '',
      treatmentHistory: '',
      yearOfDiagnosis: '',
      diagnosisDuration: '',
      durationUnit: 'Months',
      status: 'active',
      smokingHistory: 'no',
      smokingPackYears: '',
      smokingFrequency: 'Daily',
      alcoholConsumption: 'no',
      oralTobacco: 'no',
      drugs: 'no',
    },
  });

  const smokingHistory = watch('smokingHistory');
  const smokingPackYears = watch('smokingPackYears');
  const smokingFrequency = watch('smokingFrequency');

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
  ];

  const historyOptions = [
    { value: 'no', label: 'No' },
    { value: 'former', label: 'Former' },
    { value: 'current', label: 'Current' },
  ];

  const onSubmit = (data: FormValues) => {
    console.log('Form data:', data);
    setIsViewMode(true);
  };

  if (isViewMode) {
    return (
      <div className="flex flex-col h-full">
        <MedicalHistoryAddictionViewComponent />
        <div className="p-4">
          <div className="flex justify-end">
            <PrimaryButton
              variant="outlined"
              onClick={() => setIsViewMode(false)}
            >
              Edit Details
            </PrimaryButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col flex-grow"
      >
        <div className="flex-grow p-4 space-y-4 overflow-y-auto">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-sm mb-1">Name of Disease</label>
                <AutoResizeTextArea
                  control={control}
                  name="disease"
                  placeholder="Enter disease name"
                  className="w-full"
                />
              </div>
              <div className="w-48">
                <label className="block text-sm mb-1">Year of diagnosis</label>
                <select
                  {...register('yearOfDiagnosis')}
                  className="w-full border rounded-md p-2"
                >
                  <option value="">YYYY</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>
              <div className="w-48">
                <label className="block text-sm mb-1">Diagnosis duration</label>
                <AutoResizeTextArea
                  control={control}
                  name="diagnosisDuration"
                  placeholder="Enter duration"
                  className="w-full"
                />
              </div>
              <div className="mt-6">
                <CustomRadioGroup
                  options={statusOptions}
                  register={register('status')}
                  errors={errors.status}
                  onChange={(e) => {
                    setValue(
                      'status',
                      e.target.value as 'active' | 'inactive',
                      {
                        shouldValidate: isSubmitted,
                      }
                    );
                  }}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Treatment history</label>
              <AutoResizeTextArea
                control={control}
                name="treatmentHistory"
                placeholder="Enter treatment history"
                className="w-full"
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <CustomRadioGroup
                  label="Smoking History"
                  options={historyOptions}
                  register={register('smokingHistory')}
                  errors={errors.smokingHistory}
                  onChange={(e) => {
                    setValue(
                      'smokingHistory',
                      e.target.value as 'no' | 'former' | 'current',
                      { shouldValidate: isSubmitted }
                    );
                    if (e.target.value === 'no') {
                      setValue('smokingPackYears', '');
                      setValue('smokingFrequency', '');
                    }
                  }}
                />
              </div>

              {smokingHistory !== 'no' && (
                <div className="flex gap-4 items-center">
                  <div>
                    <label className="block text-sm mb-1">
                      Smoking Pack Years
                    </label>
                    <input
                      type="number"
                      {...register('smokingPackYears')}
                      className="border rounded-md p-2 w-24"
                    />
                  </div>
                  {smokingPackYears && (
                    <div>
                      <label className="block text-sm mb-1">Frequency</label>
                      <select
                        {...register('smokingFrequency')}
                        className="border rounded-md p-2"
                      >
                        <option>Daily</option>
                        <option>Weekly</option>
                        <option>Monthly</option>
                      </select>
                    </div>
                  )}
                  {smokingFrequency === 'Daily' && smokingPackYears && (
                    <button
                      onClick={() => setIsNicotineTestOpen(true)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md mt-6"
                    >
                      Nicotine Dependence Test
                    </button>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-4">
              <CustomRadioGroup
                label="Alcohol Consumption"
                options={historyOptions}
                register={register('alcoholConsumption')}
                errors={errors.alcoholConsumption}
                onChange={(e) => {
                  setValue(
                    'alcoholConsumption',
                    e.target.value as 'no' | 'former' | 'current',
                    { shouldValidate: isSubmitted }
                  );
                }}
              />
            </div>

            <div className="flex items-center gap-4">
              <CustomRadioGroup
                label="Oral Tobacco"
                options={historyOptions}
                register={register('oralTobacco')}
                errors={errors.oralTobacco}
                onChange={(e) => {
                  setValue(
                    'oralTobacco',
                    e.target.value as 'no' | 'former' | 'current',
                    { shouldValidate: isSubmitted }
                  );
                }}
              />
            </div>

            <div className="flex items-center gap-4">
              <CustomRadioGroup
                label="Drugs"
                options={historyOptions}
                register={register('drugs')}
                errors={errors.drugs}
                onChange={(e) => {
                  setValue(
                    'drugs',
                    e.target.value as 'no' | 'former' | 'current',
                    {
                      shouldValidate: isSubmitted,
                    }
                  );
                }}
              />
            </div>
          </div>
        </div>

        <div className="p-4">
          <div className="flex justify-end gap-4">
            <PrimaryButton
              variant="outlined"
              onClick={() => setIsViewMode(true)}
            >
              Cancel
            </PrimaryButton>
            <PrimaryButton type="submit">Save Changes</PrimaryButton>
          </div>
        </div>
      </form>

      <NicotineDependenceTest
        isOpen={isNicotineTestOpen}
        onClose={() => setIsNicotineTestOpen(false)}
      />
    </div>
  );
};

export default memo(MedicalHistoryAddictionView);
