import { memo, useMemo } from 'react';

import { LockOpenIcon } from 'lucide-react';

import { formatDate } from '@/utils/dateUtils/dayUtils';

import ExpandSimpleIcon from '@/assets/svg/ExpandSimpleIcon';
import PrintIcon from '@/assets/svg/PrintIcon';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import AppIconButton from '@/core/components/app-icon-button';
import AppModal from '@/core/components/app-modal';
import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternModal from './ExercisePatternModal';

type Props = {
  open: boolean;
  onClose: () => void;
  formFields: QuestionnaireResponse | null;
  onFinalize: (data: QuestionnaireResponse | null) => void;
};

const ExercisePatternEditModal = ({
  open,
  onClose,
  formFields,
  onFinalize,
}: Props) => {
  const doctorFirstLetter = useMemo(() => {
    return (
      formFields?.doctor?.name
        ?.split(' ')
        .map((n) => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2) || '--'
    );
  }, [formFields?.doctor?.name]);

  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Edit Record"
      classes={{
        root: 'w-[60vw] h-[80vh] flex flex-col min-h-0',
        body: 'flex-1 h-full flex flex-col min-h-0 !p-0',
        header: '!hidden',
      }}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center w-full h-10 p-base bg-[#B4E5FE] rounded-base">
          <div className="flex items-center gap-3 text-blue-900 w-full">
            <AppIconButton
              variant="outlined"
              sx={{ backgroundColor: 'white !important' }}
            >
              <div className="w-10 h-10 rounded-full bg-transparent flex items-center justify-center text-[#6B7280] font-semibold text-base">
                {doctorFirstLetter}
              </div>
            </AppIconButton>
            <div className="flex flex-row items-center gap-4 w-full">
              <span className="font-archivo font-medium text-xl text-[#001926] leading-none">
                {formFields?.doctor?.name}
              </span>
              {formFields?.doctor?.designation && (
                <span className="text-[#64707D] text-base font-normal leading-none">
                  |
                </span>
              )}
              <span className="text-[#001926] text-base font-normal leading-none">
                {formFields?.doctor?.designation}
              </span>
              <span className="text-[#64707D] text-base font-normal leading-none">
                |
              </span>
              <span className="text-[#001926] text-base font-normal leading-none">
                {formatDate(formFields?.created_on, 'DD/MM/YYYY')}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <AppIconButton
              onClick={(e) => {
                e.stopPropagation();
                onFinalize?.(formFields);
              }}
              variant="outlined"
              sx={{ backgroundColor: 'white !important' }}
            >
              <LockOpenIcon className="text-sm" />
            </AppIconButton>

            <AppIconButton
              onClick={(e) => {
                e.stopPropagation();
              }}
              variant="outlined"
              sx={{ backgroundColor: 'white !important' }}
            >
              <PrintIcon />
            </AppIconButton>
            <AppIconButton
              onClick={(e) => {
                e.stopPropagation();
                onClose?.();
              }}
              variant="outlined"
              sx={{ backgroundColor: 'white !important' }}
            >
              <ExpandSimpleIcon className="text-sm" />
            </AppIconButton>
          </div>
        </div>
        <div className="flex-1 h-full flex flex-col min-h-0 p-base">
          <ExercisePatternModal
            patientData={formFields}
            mode={LifestyleMode.VIEW}
            onAfterSubmit={() => {
              onClose();
            }}
          />
        </div>
      </div>
    </AppModal>
  );
};

export default memo(ExercisePatternEditModal);
