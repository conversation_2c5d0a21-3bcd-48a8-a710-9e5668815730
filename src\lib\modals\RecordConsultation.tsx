'use client';

import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import * as Dialog from '@radix-ui/react-dialog';
import { compact } from 'lodash';
import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiTrash, BiX } from 'react-icons/bi';
import { TbPlayerPause, TbPlayerPlay } from 'react-icons/tb';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';

import { throttledUpdateLastActivity } from '@/utils/session';

import SineWaves from '@/helpers/sine-waves/sine-waves';

import {
  recordingStates,
  RecordingState,
  waves,
  languageOptions,
  CurrentModal,
  currentModal,
} from '@/constants/ambient-listening';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';

interface RecordConsultationProps {
  engine: speechsdk.ConversationTranscriber | null;
  onRecordEnd: (transcript: string) => void;
  handleChooseLanguage: (newLanguage: string) => void;
  selectedLanguage: string;
  currentMode: CurrentModal;
  setCurrentMode: (mode: CurrentModal) => void;
}

const { IDLE, PAUSED, RECORDING } = recordingStates;
const { LANGUAGE_SELECTION, RECORD_CONSULTATION } = currentModal;

const PERIODIC_ACTION_INTERVAL = 5 * 60 * 1000;

const RecordConsultation: FC<RecordConsultationProps> = ({
  engine,
  onRecordEnd,
  handleChooseLanguage,
  selectedLanguage,
  setCurrentMode,
  currentMode,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [recordingState, setRecordingState] = useState<RecordingState>(IDLE);
  const [wavesConfig, setWavesConfig] = useState({
    speed: 0,
    wavesWidth: '0',
  });

  //speech states
  const [currentOffset, setCurrentOffset] = useState(0);
  const [isListening, setIsListening] = useState(false);

  const [transcriptHistory, setTranscriptHistory] = useState(['']);
  const [transcript, setTranscript] = useState('');
  const [tEvent, setTvent] = useState(null);

  const transcriptEl = useRef(null);

  const wavesEl = useRef(null);

  const selectedLanguageOption = useMemo(
    () => languageOptions?.find((option) => option.value === selectedLanguage),
    [selectedLanguage]
  );

  const handlePeriodicAction = useCallback(() => {
    getDoctorProfile();
  }, []);

  const handleTranscribing = useCallback(
    (s: any, e: any) => {
      throttledUpdateLastActivity();
      if (e && e.privResult && e.privResult.privJson) {
        const event = JSON.parse(e.privResult.privJson);
        if (transcriptEl.current) {
          (transcriptEl.current as HTMLDivElement).scrollTop = (
            transcriptEl.current as HTMLDivElement
          ).scrollHeight;
        }
        setTvent(() => event);
      }
    },
    [transcriptEl]
  );

  useEffect(() => {
    if (engine) {
      engine.transcribing = handleTranscribing;
    }

    return () => {
      setTranscript('');
      setTranscriptHistory(['']);
      if (engine) {
        engine.stopTranscribingAsync();
      }
      // Clear interval when component unmounts
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [engine, handleTranscribing]);

  useEffect(() => {
    if (tEvent) {
      const { Type, Offset, Text } = tEvent;
      if (Type === 'ConversationTranscription') {
        setTranscript(() => {
          if (Offset !== currentOffset) {
            const newHist = [...transcriptHistory, transcript];

            setTranscriptHistory(newHist);
            setCurrentOffset(Offset);
          }
          return Text;
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tEvent]);

  const toggleRecord = () => {
    if (isListening) {
      setIsListening(false);
      engine?.stopTranscribingAsync(() => {
        setRecordingState(PAUSED);
      });
    } else {
      setIsListening(true);
      engine?.startTranscribingAsync(() => {
        setRecordingState(RECORDING);
      });
    }
  };

  useEffect(() => {
    if (wavesEl.current != null && typeof window !== 'undefined') {
      new SineWaves({
        el: wavesEl.current,

        speed: wavesConfig.speed,

        ease: 'SineInOut',

        wavesWidth: wavesConfig.wavesWidth,

        waves: waves,

        // Called on window resize
        resizeEvent: function () {
          var gradient = this.ctx.createLinearGradient(0, 0, this.width, 0);
          gradient.addColorStop(0, 'rgba(25, 255, 255, 0)');
          gradient.addColorStop(0.5, 'rgba(100, 100, 255, 1)');
          gradient.addColorStop(1, 'rgba(255, 255, 255, 1)');

          var index = -1;
          var length = this.waves.length;
          while (++index < length) {
            this.waves[index].strokeStyle = gradient;
          }

          index = 0;
          length = 0;
          gradient = 0;
        },
      });
    }
  }, [wavesConfig.speed, wavesConfig.wavesWidth]);

  const handleStartRecord = () => {
    setCurrentMode(RECORD_CONSULTATION);
    setRecordingState(RECORDING);
    toggleRecord();
    setWavesConfig({
      speed: 5,
      wavesWidth: '75%',
    });
    // Start periodic action every 5 minutes
    intervalRef.current = setInterval(
      handlePeriodicAction,
      PERIODIC_ACTION_INTERVAL
    );
  };

  const handlePauseRecord = () => {
    setRecordingState(PAUSED);
    toggleRecord();
    setWavesConfig({
      speed: 0,
      wavesWidth: '0%',
    });
    // Clear interval when recording is paused
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  return (
    <div className="w-full min-h-full flex flex-col justify-between flex-1">
      <div className="px-2.5">
        <div className="pb-2.5 px-2.5 border-b">
          <span className="text-xl">
            {currentMode === LANGUAGE_SELECTION
              ? 'Recording Preference'
              : 'Recording Consultation'}
          </span>
        </div>

        {recordingState === IDLE && currentMode === LANGUAGE_SELECTION && (
          <div className="h-full">
            <div className="w-1/2 p-2 flex flex-col gap-2">
              <span>Change Input Language</span>
              <Select
                onValueChange={handleChooseLanguage}
                value={'selectedLanguageOption?.label'}
              >
                <SelectTrigger autoFocus={false}>
                  {selectedLanguageOption?.label ?? 'Select Language'}
                </SelectTrigger>

                <SelectContent>
                  {languageOptions.map((language) => (
                    <SelectItem key={language.value} value={language.value}>
                      {language.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
        {currentMode === RECORD_CONSULTATION && (
          <div
            className="h-[40vh] flex flex-col gap-2 overflow-y-auto py-5"
            ref={transcriptEl}
          >
            <div className="gap-3">
              {compact([...transcriptHistory, transcript]).map(
                (x, i) => x && <p key={i}>{x}</p>
              )}
            </div>
          </div>
        )}
        <div className="flex flex-col items-center justify-center">
          {currentMode !== LANGUAGE_SELECTION && (
            <canvas ref={wavesEl} className="w-full h-20"></canvas>
          )}
          {currentMode !== LANGUAGE_SELECTION && (
            <span className="text-xs">Ambient recording active...</span>
          )}
        </div>
      </div>
      <div className="rounded-lg border border-[#637D92] py-4 px-5 flex items-center justify-between">
        <>
          <div className="flex items-center gap-5">
            {(recordingState === RECORDING || recordingState === PAUSED) && (
              <>
                <Dialog.Close asChild>
                  <button className="py-2.5 px-5 border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px]">
                    Cancel Record
                    <BiTrash />
                  </button>
                </Dialog.Close>
                <button
                  className="py-2.5 px-5 border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px]"
                  onClick={() =>
                    onRecordEnd([...transcriptHistory, transcript].join(' '))
                  }
                >
                  End Record
                  <BiX className="text-xl" />
                </button>
              </>
            )}
          </div>
          {recordingState == RECORDING && (
            <button
              className="py-2.5 px-5 bg-black text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] border border-transparent"
              onClick={handlePauseRecord}
            >
              Pause Recording
              <TbPlayerPause className="text-lg" />
            </button>
          )}
          {recordingState !== RECORDING && (
            <button
              className="py-2.5 px-5 bg-black text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] border border-transparent"
              onClick={handleStartRecord}
            >
              Start Recording
              <TbPlayerPlay className="text-lg" />
            </button>
          )}
        </>
      </div>
    </div>
  );
};
export default RecordConsultation;
