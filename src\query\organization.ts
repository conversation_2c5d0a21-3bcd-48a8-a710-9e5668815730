import { api } from '@/core/lib/interceptor';

export interface Organization {
  id: string;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contactEmail: string;
  contactPhone: string;
  contactPersonName: string;
  isActive: boolean;
}

export const organizationQueries = {
  getOrganization: async (organizationId: string): Promise<Organization> => {
    try {
      const response = await api.get<Organization>(
        `/organization/v0.1/organization?organizationId=${organizationId}`
      );

      return response.data;
    } catch (error) {
      console.error('Error in organizationQueries.getOrganization:', error);
      throw error;
    }
  },
};
