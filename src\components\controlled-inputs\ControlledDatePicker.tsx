import React from 'react';

import { Control, Controller, FieldValues, Path } from 'react-hook-form';

import AppDatePicker, {
  AppDatePickerProps,
} from '@core/components/app-date-picker';

export type ControlledDatePickerProps<T extends FieldValues> = Omit<
  AppDatePickerProps,
  'value' | 'onChange'
> & {
  name: Path<T>;
  control: Control<T>;
};

const ControlledDatePicker = <T extends FieldValues>({
  name,
  control,
  ...props
}: ControlledDatePickerProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <AppDatePicker
          {...props}
          value={field.value}
          onChange={field.onChange}
          error={props.error || fieldState.error?.message}
        />
      )}
    />
  );
};

export default ControlledDatePicker;
