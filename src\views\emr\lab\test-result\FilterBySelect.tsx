import { FC, memo, useCallback, useState } from 'react';

import {
  DateRange as ReactDateRange,
  Range,
  RangeKeyDict,
} from 'react-date-range';

import { Modal } from '@mui/material';
import dayjs, { Dayjs } from 'dayjs';

import '@core/components/date-range-picker/styles.scss';

import { FilterByType, filterOptions, filterBy } from '@/utils/filter-by-util';

import CommonSelectDropdown, {
  OptionType,
} from '../packages/DepartmentDropdown';

import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

type Props = {
  value: FilterByType | null;
  onChange: (val: FilterByType, customDate?: Dayjs[]) => void;
  selectedDateRange?: (Dayjs | null)[];
};

const { CUSTOM_DATE } = filterBy;

const FilterBySelect: FC<Props> = ({ value, onChange, selectedDateRange }) => {
  const [dateRangeOpen, setDateRangeOpen] = useState(false);
  const [dateRange, setDateRange] = useState<Range[] | undefined>([
    {
      startDate: dayjs(selectedDateRange?.[0]).toDate(),
      endDate: dayjs(selectedDateRange?.[1]).toDate(),
      key: 'selection',
    },
  ]);

  const handleValueChange = useCallback(
    (value: FilterByType) => {
      if (value === CUSTOM_DATE) {
        return;
      } else {
        onChange(value);
        setDateRange([
          {
            startDate: dayjs().toDate(),
            endDate: dayjs().toDate(),
            key: 'selection',
          },
        ]);
        return;
      }
    },
    [onChange]
  );

  const handleSelectDate = useCallback(
    (rangesByKey: RangeKeyDict) => {
      let startDate = dayjs(rangesByKey?.selection?.startDate);
      let endDate = dayjs(rangesByKey?.selection?.endDate);

      if (!startDate.isValid()) {
        startDate = dayjs(selectedDateRange?.[0]);
      }

      if (!endDate.isValid()) {
        endDate = dayjs(selectedDateRange?.[1]);
      }

      if (!startDate.isValid() || !endDate.isValid()) return;

      setDateRange([
        {
          startDate: startDate.toDate(),
          endDate: endDate.toDate(),
          key: 'selection',
        },
      ]);
    },
    [selectedDateRange]
  );

  const handleDatePickerClose = useCallback(() => {
    setDateRangeOpen(false);
    if (dateRange?.[0]) {
      const startDate = dayjs(dateRange[0].startDate);
      const endDate = dayjs(dateRange[0].endDate);
      if (startDate.isValid() && endDate.isValid()) {
        onChange(CUSTOM_DATE, [startDate, endDate]);
      }
    }
  }, [onChange, dateRange]);

  const handleClickOnMenu = useCallback((value: OptionType) => {
    if (value.key === CUSTOM_DATE) {
      setDateRangeOpen(true);
    }
  }, []);

  return (
    <>
      <CommonSelectDropdown
        name="filterBy"
        value={value ?? ''}
        placeholder="Filter by"
        options={filterOptions}
        onChange={(value) => handleValueChange(value as FilterByType)}
        className="w-full"
        sx={{ '& .MuiSelect-outlined': { px: '4px !important' } }}
        onClickMenu={handleClickOnMenu}
      />
      <Modal
        open={dateRangeOpen}
        onClose={handleDatePickerClose}
        classes={{ root: 'w-screen h-screen flex items-center justify-center' }}
      >
        <div className="w-fit h-fit p-1 bg-white rounded-md">
          <ReactDateRange
            editableDateInputs={false}
            onChange={handleSelectDate}
            moveRangeOnFirstSelection={false}
            ranges={dateRange}
          />
        </div>
      </Modal>
    </>
  );
};

export default memo(FilterBySelect);
