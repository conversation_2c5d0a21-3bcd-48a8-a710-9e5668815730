import {
  ButtonHTMLAttributes,
  DetailedHTMLProps,
  FC,
  useEffect,
  useState,
  useRef,
} from 'react';

import { IoMdCheckmark } from 'react-icons/io';
import { IoClose } from 'react-icons/io5';
import { MdDragIndicator } from 'react-icons/md';

import { calculateAge } from '@/utils/mrd/manage-patient/calculate-age';
import { formatAddress } from '@/utils/mrd/manage-patient/format-address';

import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

import Avatar from '@/views/mrd/queue/queue-card/avatar';

type QueueCardPatient = {
  name: string;
  sex: string;
  address: string;
  age: number;
  id: string;
  dob: string;
};

export interface QueueCardProps {
  appointmentId?: string;
  queueId: string;
  patientId: string;
  patient: QueueCardPatient;
  queuePosition: number;
  status: AppointmentStatus;
  patientStatus?: PatientStatus;
  time: string;
  isSelected?: boolean;
  isPatientFromQueue?: boolean;
  onClickMove?: (data: QueueCardProps) => void;
  onClickMarkAsConsulted?: (data: QueueCardProps) => void;
  onClickCancel?: (data: QueueCardProps) => void;
  onMoveDown?: (item?: QueueCardProps) => Promise<void>;
  onSelect?: (e: React.MouseEvent) => void;
  onClick?: () => void;
}

const QueueCard: FC<QueueCardProps> = (props) => {
  const {
    patient,
    status,
    patientStatus,
    queuePosition,
    isSelected = false,
    isPatientFromQueue = false,
    onClickMove = () => {},
    onClickMarkAsConsulted = () => {},
    onClickCancel = () => {},
    onMoveDown = () => {},
    onSelect = () => {},
  } = props;

  const cardRef = useRef<HTMLDivElement>(null);

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger selection if clicking on buttons or links
    const target = e.target as HTMLElement;
    if (
      !(target instanceof HTMLButtonElement) &&
      !(target instanceof HTMLAnchorElement) &&
      !(target instanceof SVGElement) &&
      !(target.parentElement instanceof HTMLButtonElement) &&
      !(target.parentElement instanceof HTMLAnchorElement)
    ) {
      onSelect?.(e);
    }
  };

  const [localStatus, setLocalStatus] = useState<'active' | 'inQueue'>(
    'active'
  );

  const handleClickMove = () => {
    onClickMove(props);
  };

  const handleClickMarkAsConsulted = () => {
    onClickMarkAsConsulted(props);
  };

  const handleClickCancel = () => {
    onClickCancel(props);
  };

  useEffect(() => {
    if (status === AppointmentStatus.Consultation) {
      setLocalStatus('active');
    } else {
      setLocalStatus('inQueue');
    }
  }, [status]);

  const Separator = ({ className = '' }) => {
    return <span className={`text-[#C2CDD6] ${className}`}>|</span>;
  };

  // Only show blue border when explicitly selected
  const borderClass =
    isSelected && isPatientFromQueue
      ? 'border border-[#53BDF5]'
      : 'border border-[#DAE1E7]';

  return (
    <div
      ref={cardRef}
      onClick={handleCardClick}
      className={`py-1 w-full px-1 flex items-center rounded-[5px] bg-white ${borderClass} grid grid-cols-12 cursor-pointer `}
    >
      <div className="col-span-6 flex items-center gap-1 py-1 w-full">
        <div className="w-4">
          <MdDragIndicator className="cursor-pointer text-lg text-[#64707D] col-span-1" />
        </div>

        <div className="w-8 2xl:w-10">
          <Avatar
            name={patient?.name}
            status={status}
            patientStatus={patientStatus}
            queuePosition={queuePosition}
          />
        </div>

        <div className="flex flex-col text-[#001926] w-[calc(100%-3.5rem)] 2xl:w-[calc(100%-4rem)]">
          <span className="text-sm 2xl:text-base capitalize font-medium -tracking-[2.2%] w-full  text-[14px]">
            {patient?.name}
          </span>
          <div className="flex items-center gap-1 whitespace-nowrap w-full text-[#64707D]">
            <span className="font-light text-xs -tracking-[2.2%] w-fit">
              {calculateAge(patient?.dob)} yrs
            </span>
            <Separator className="h-4" />
            <span className="font-light text-xs -tracking-[2.2%] w-fit">
              {patient?.sex && typeof patient?.sex === 'string'
                ? patient?.sex.charAt(0).toUpperCase()
                : ''}
            </span>
            <Separator className="h-4" />
            <span className="font-light text-xs -tracking-[2.2%] w-full truncate">
              {formatAddress(patient?.address)}
            </span>
          </div>
        </div>
      </div>

      <div className="flex px-2 2xl:px-3 gap-1 col-span-6 border-l border-[#C2CDD6]  overflow-hidden">
        {localStatus === 'active' && (
          <>
            <OutLinedButton onClick={() => onMoveDown(props)}>
              Move to queue
            </OutLinedButton>
            <OutLinedButton onClick={handleClickMarkAsConsulted}>
              Mark as Consulted <IoMdCheckmark />
            </OutLinedButton>
          </>
        )}

        {localStatus === 'inQueue' && (
          <>
            <OutLinedButton onClick={handleClickMove}>
              Consult Now
            </OutLinedButton>

            <OutLinedButton onClick={handleClickCancel}>
              Cancel <IoClose />
            </OutLinedButton>
          </>
        )}
      </div>
    </div>
  );
};

// Export the component as default
export default QueueCard;

// Export the types
export type { QueueCardPatient };

const OutLinedButton: React.FC<
  DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>
> = ({ children, ...props }) => {
  return (
    <button
      {...props}
      type={props.type as 'submit' | 'reset' | 'button' | undefined}
      className="flex-1 rounded-full flex justify-evenly items-center  whitespace-nowrap border border-black py-1 px-1 font-light -tracking-[2.2%]  text-[11px] 2xl:text-xs"
    >
      {children}
    </button>
  );
};
