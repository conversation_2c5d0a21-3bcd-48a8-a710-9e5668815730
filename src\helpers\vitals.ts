import { formatDistanceToNow } from 'date-fns';

export type Vital = {
  bmi?: string;
  bloodPressure?: string;
  height?: number;
  weight?: string;
  heartRate?: number;
  respiratoryRate?: string;
  updatedOn?: string;
  pulse?: string;
  rr?: string;
  spO2?: string;
  pp?: string;
};

export type VitalAnalyticsValue =
  | {
      type: 'lastRecorded';
      value: string;
    }
  | {
      type: 'trend';
      trend: 'inc' | 'dec' | 'flat';
      value: string;
    };

export type VitalAnalytics = Record<
  keyof Omit<Vital, 'updatedOn' | 'pulse' | 'rr' | 'spO2' | 'pp'>,
  VitalAnalyticsValue
> & {
  spO2?: VitalAnalyticsValue;
  pp?: VitalAnalyticsValue;
};

const calculatePercentageChange = (
  prevValue: number,
  currentValue: number
): number => {
  if (prevValue === 0 && currentValue > 0) {
    return 100;
  }

  if (!prevValue) {
    return 0;
  }

  return ((currentValue - prevValue) / prevValue) * 100;
};

const getPercentageChangeAnalytics = (
  prevVitalValue?: number,
  currentVitalValue?: number
): VitalAnalyticsValue => {
  const result: VitalAnalyticsValue = {
    type: 'trend',
    trend: 'flat',
    value: '',
  };

  if (
    typeof prevVitalValue !== 'number' ||
    typeof currentVitalValue !== 'number' ||
    !currentVitalValue
  ) {
    return result;
  }

  const change = calculatePercentageChange(prevVitalValue, currentVitalValue);

  if (Number.isInteger(change) && change !== 0) {
    result.value = `${Math.abs(change)} %`;
  } else {
    result.value = `${Math.abs(change).toFixed(1)} %`;
  }

  if (change === 0) {
    result.trend = 'flat';
  } else if (change > 0) {
    result.trend = 'inc';
  } else {
    result.trend = 'dec';
  }

  return result;
};

function getLastRecordedAnalytics(prevVitalDate?: string): VitalAnalyticsValue {
  const result: VitalAnalytics['bmi'] = {
    type: 'lastRecorded',
    value: '',
  };

  if (!prevVitalDate) {
    return result;
  }

  try {
    result.value = formatDistanceToNow(new Date(prevVitalDate), {
      addSuffix: true,
    });
  } catch {}

  result.value = result.value.replace('minutes', 'mins');
  result.value = result.value.replace('minute', 'min');
  result.value = result.value.replace('hours', 'hrs');
  result.value = result.value.replace('hour', 'hr');
  result.value = result.value.replace('about', '');
  result.value = result.value.replace('less than', '');
  result.value = result.value.replace('less than a', '');
  result.value = result.value.trim();

  return result;
}

/**
 * Calculate trend from a list of vitals
 */
export const calculateVitalsAnalytics = (history?: Vital[]): VitalAnalytics => {
  const DEFAULT_ANALYTIC_VALUE: VitalAnalyticsValue = {
    type: 'lastRecorded',
    value: '',
  };

  const analytics: VitalAnalytics = {
    bmi: DEFAULT_ANALYTIC_VALUE,
    bloodPressure: DEFAULT_ANALYTIC_VALUE,
    height: DEFAULT_ANALYTIC_VALUE,
    weight: DEFAULT_ANALYTIC_VALUE,
    heartRate: DEFAULT_ANALYTIC_VALUE,
    respiratoryRate: DEFAULT_ANALYTIC_VALUE,
    spO2: DEFAULT_ANALYTIC_VALUE,
    pp: DEFAULT_ANALYTIC_VALUE,
  };

  if (!history?.length || history?.length < 2) {
    return analytics;
  }

  const latestValue = history[history.length - 1];
  const prevValue = history[history.length - 2];

  analytics.height = getPercentageChangeAnalytics(
    Number(prevValue?.height),
    Number(latestValue?.height)
  );
  analytics.weight = getPercentageChangeAnalytics(
    Number(prevValue?.weight),
    Number(latestValue?.weight)
  );
  analytics.bmi = getPercentageChangeAnalytics(
    Number(prevValue?.bmi),
    Number(latestValue?.bmi)
  );
  analytics.bloodPressure = getLastRecordedAnalytics(latestValue?.updatedOn);
  analytics.heartRate = getLastRecordedAnalytics(latestValue?.updatedOn);
  analytics.respiratoryRate = getLastRecordedAnalytics(latestValue?.updatedOn);
  analytics.spO2 = getLastRecordedAnalytics(latestValue?.updatedOn);
  analytics.pp = getLastRecordedAnalytics(latestValue?.updatedOn);

  return analytics;
};

export const calculateAnalyticsOfVitals = (
  history?: Vital[],
  selectedTiles?: string[]
): Record<string, VitalAnalyticsValue> => {
  if (!history?.length || history.length < 2 || !selectedTiles?.length) {
    return {};
  }

  const latestValue = history[history.length - 1];
  const prevValue = history[history.length - 2];

  return selectedTiles.reduce((acc, tile) => {
    const vitalKey = tile as keyof VitalAnalytics;

    if (['height', 'weight', 'bodyMassIndex'].includes(tile)) {
      const prev =
        tile === 'bodyMassIndex' ? prevValue?.bmi : prevValue?.[vitalKey];
      const latest =
        tile === 'bodyMassIndex' ? latestValue?.bmi : latestValue?.[vitalKey];

      acc[vitalKey] = getPercentageChangeAnalytics(
        Number(prev),
        Number(latest)
      );
    } else {
      acc[vitalKey] = getLastRecordedAnalytics(latestValue?.updatedOn);
    }

    return acc;
  }, {} as VitalAnalytics);
};
