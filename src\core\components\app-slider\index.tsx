import React, { useCallback } from 'react';

import { FieldError } from 'react-hook-form';

import { SliderProps, Typography, Box } from '@mui/material';

import { cn } from '@/lib/utils';

import InputLabel from '../input-label';

import { Indicator, MuiSlider } from './styled-components';

export interface AppSliderProps extends Omit<SliderProps, 'onChange'> {
  value?: number | number[];
  onChange?: (value: number | number[]) => void;
  label?: string;
  helperText?: string;
  errors?: FieldError;
  minLabel?: string;
  maxLabel?: string;
  required?: boolean;
  showIndicator?: boolean;
  labelVariant?: 'horizontal' | 'vertical';
}

const AppSlider: React.FC<AppSliderProps> = ({
  value,
  onChange,
  label,
  helperText,
  errors,
  min = 0,
  max = 10,
  step = 1,
  minLabel,
  maxLabel,
  required = false,
  showIndicator = false,
  labelVariant = 'horizontal',
  ...rest
}) => {
  const handleChange = (_: Event, newValue: number | number[]) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  const renderIndicator = useCallback(() => {
    if (!showIndicator) return null;

    const showIndicatorValue =
      (Array.isArray(value) ? value[0] : (value ?? 0)) ?? 0;

    return (
      <Indicator>
        {Array.from({ length: max + 1 }, (_, i) => i).map((num) => (
          <Box key={num} className="flex flex-col items-center gap-1">
            <div
              className={cn(
                `w-0.5 h-5 bg-sky-400 rounded transition-all z-[1]`,
                { 'bg-transparent': showIndicatorValue >= num },
                { 'bg-gray-400': rest.disabled && showIndicatorValue < num }
              )}
            />
            <Typography variant="body1" fontWeight={700} color="textSecondary">
              {num}
            </Typography>
          </Box>
        ))}
      </Indicator>
    );
  }, [showIndicator, max, value, rest.disabled]);

  return (
    <Box
      className={cn('w-full space-y-2', {
        'flex flex-col': labelVariant === 'vertical',
        'flex flex-row gap-base items-center': labelVariant === 'horizontal',
      })}
    >
      {label && (
        <InputLabel label={label} required={required} className="mb-2" />
      )}
      <div className="w-full space-y-2">
        <Box sx={{ px: 1, width: '100%', position: 'relative', minHeight: 30 }}>
          <MuiSlider
            value={value ?? min}
            onChange={handleChange}
            min={min}
            max={max}
            step={step}
            valueLabelFormat={(value) => value}
            sx={{
              '& .MuiSlider-valueLabel': {
                backgroundColor: 'primary.main',
                '&:before': { display: 'none' },
              },
            }}
            {...rest}
          />
          {renderIndicator()}
        </Box>
        <Box display="flex" justifyContent="space-between" mt={0.5}>
          {minLabel && (
            <Typography variant="caption" color="textSecondary">
              {minLabel}
            </Typography>
          )}
          {maxLabel && (
            <Typography variant="caption" color="textSecondary">
              {maxLabel}
            </Typography>
          )}
        </Box>
        {(helperText || errors?.message) && (
          <Typography
            variant="caption"
            color={errors ? 'error' : 'textSecondary'}
            display="block"
            mt={1}
          >
            {errors?.message || helperText}
          </Typography>
        )}
      </div>
    </Box>
  );
};

export default AppSlider;
