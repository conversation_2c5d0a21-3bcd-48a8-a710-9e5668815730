import dynamic from 'next/dynamic';

import appMetadata from '@/core/configs/app-metadata';
import Loading from '@/lib/common/loading';

// This is a server component that only handles metadata
export const metadata = appMetadata.mrd;

// Dynamically import the client component with SSR disabled
const MrdLayoutClient = dynamic(
  () => import('@/core/layout/mrd/MrdLayoutWrapper'),
  { ssr: false, loading: () => <Loading/> }
);

// This is the main layout component that will be used by Next.js
export default function MrdLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return <MrdLayoutClient>{children}</MrdLayoutClient>;
}
