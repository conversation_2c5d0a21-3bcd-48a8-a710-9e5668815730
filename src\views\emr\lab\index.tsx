import React from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import { pageIds } from '@/utils/constants/docAssist';

import VitalsTab from '@/emr/components/consultation/vitals-tab';
import DocAssist from '@/emr/components/doc_assist';
import PatientCard from '@/emr/components/patient-card';

import RecentTests from './recent-tests';
import TestTabSection from './TestTabSection';

const TestReports = () => {
  const { patient } = useCurrentPatientStore();

  return (
    <div className="flex w-full h-full gap-base">
      <div className="w-75 flex-shrink-0 flex-grow-0 bg-white rounded-base h-full flex flex-col gap-1 shadow-base overflow-hidden mr-2">
        <div className="p-3 border-b border-gray-200 shadow-md rounded-md">
          <h2 className="text-sm font-semibold text-gray-700">Lab Master</h2>
          <PatientCard
            name={patient?.name ?? '--'}
            dob={patient?.dob}
            sex={patient?.sex ?? '--'}
            address={patient?.address}
          />
        </div>
        <div className="max-h-[40%] overflow-y-auto px-2">
          <VitalsTab />
        </div>
        <div className="flex-1 overflow-hidden">
          <RecentTests />
        </div>
      </div>
      <div className="h-full flex-1 flex-grow bg-white rounded-base border shadow-base overflow-hidden border-[#C2CDD6] min-w-0">
        <TestTabSection />
      </div>
      <div className="h-full bg-white rounded-base border overflow-x-hidden flex-shrink-0 overflow-y-auto w-[18%] border-[#C2CDD6] shadow-base min-w-0">
        <DocAssist pageId={pageIds.REPORTS} />
      </div>
    </div>
  );
};

export default TestReports;
