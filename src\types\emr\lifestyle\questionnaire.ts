import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

export interface BaseField {
  id: string;
  label?: string;
  type: LifestyleFields;
  required?: boolean;
  disabled?: boolean;
  description?: string;
  value?: any;
}

export interface RadioField extends BaseField {
  type: 'radio';
  options: string[];
  allowOtherSpecify?: boolean;
  modal?: {
    title: string;
    inputType: 'text' | 'number';
  };
}

export interface SliderField extends BaseField {
  type: 'slider';
  min: number;
  max: number;
  step: number;
}

export interface NumberField extends BaseField {
  type: 'number';
  min?: number;
  max?: number;
}

export interface TextField extends BaseField {
  type: 'text';
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  placeholder?: string;
}

export interface SectionField extends BaseField {
  type: 'section';
  fields?: FormField[];
}

export interface ConditionalOption {
  label?: string;
  subField?: NumberField | TextField | TextareaField;
}

export interface ConditionalField extends BaseField {
  type: 'conditional';
  conditions: ConditionalOption[];
}

export interface TableHeader {
  id: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'select'
    | 'time_range'
    | 'icon'
    | 'conditional_select';
  options?: string[] | Record<string, string[]>;
  dependsOn?: string;
  min?: number;
}

export interface TimeRange {
  from: string;
  to: string;
}

export interface TableRow {
  [key: string]: string | number | TimeRange;
}

export interface TableField extends BaseField {
  type: 'table';
  headers: TableHeader[];
  defaultRows?: TableRow[];
}

export interface MealGroup {
  id: string;
  label: string;
  defaultRows?: TableRow[];
}

export interface GroupedTableRow {
  [key: string]: string | number | TimeRange;
}

export interface GroupedTableGroup {
  id: string;
  label: string;
  rows: GroupedTableRow[];
}

export interface GroupedTableField extends BaseField {
  type: 'grouped_table';
  headers: TableHeader[];
  groupBy: string;
  mealGroups: MealGroup[];
}

export interface TimeRangeField extends BaseField {
  type: 'time_range';
}

export interface FrequencyColumn {
  header: string;
  option: string[];
}

export interface FrequencyField extends BaseField {
  type: 'frequency';
  columns: FrequencyColumn[];
}

export type FormField =
  | RadioField
  | SliderField
  | NumberField
  | TextField
  | TextareaField
  | SectionField
  | ConditionalField
  | TableField
  | GroupedTableField
  | TimeRangeField
  | FrequencyField;

export interface FieldGroup {
  id: string;
  title: string;
  icon?: string;
  fields: FormField[];
}

export type Questionnaire = {
  source: LifestyleSources;
  questions: FieldGroup[];
};

export interface Doctor {
  id: string;
  name?: string;
  designation?: string;
  department?: string;
}

export type QuestionnaireResponse = Questionnaire & {
  id: string;
  created_on: string;
  updated_on?: string;
  doctorName?: string;
  doctorDesignation?: string;
  doctor?: Doctor;
  status?: LifestyleRecordStatus;
};
