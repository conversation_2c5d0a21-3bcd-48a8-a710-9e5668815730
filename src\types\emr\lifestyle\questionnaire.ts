import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

export interface BaseField {
  id: string;
  label?: string;
  type: LifestyleFields;
  required?: boolean;
  disabled?: boolean;
  description?: string;
  value?: any;
}

export interface RadioField extends BaseField {
  type: 'radio';
  options: string[];
  allowOtherSpecify?: boolean;
  modal?: {
    title: string;
    inputType: 'text' | 'number';
  };
}

export interface SliderField extends BaseField {
  type: 'slider';
  min: number;
  max: number;
  step: number;
}

export interface NumberField extends BaseField {
  type: 'number';
  min?: number;
  max?: number;
}

export interface TextField extends BaseField {
  type: 'text';
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  placeholder?: string;
}

export interface SectionField extends BaseField {
  type: 'section';
  fields?: FormField[];
}

export interface ConditionalOption {
  label?: string;
  subField?: NumberField | TextField | TextareaField;
}

export interface ConditionalField extends BaseField {
  type: 'conditional';
  conditions: ConditionalOption[];
}

export interface TableHeader {
  id: string;
  label: string;
  type: 'text' | 'number' | 'select';
  options?: string[];
}

export interface TableRow {
  [key: string]: string | number;
}

export interface TableField extends BaseField {
  type: 'table';
  headers: TableHeader[];
  defaultRows?: TableRow[];
}

export type FormField =
  | RadioField
  | SliderField
  | NumberField
  | TextField
  | TextareaField
  | SectionField
  | ConditionalField
  | TableField;

export interface FieldGroup {
  id: string;
  title: string;
  icon?: string;
  fields: FormField[];
}

export type Questionnaire = {
  source: LifestyleSources;
  questions: FieldGroup[];
};

export interface Doctor {
  id: string;
  name?: string;
  designation?: string;
  department?: string;
}

export type QuestionnaireResponse = Questionnaire & {
  id: string;
  created_on: string;
  updated_on?: string;
  doctorName?: string;
  doctorDesignation?: string;
  doctor?: Doctor;
  status?: LifestyleRecordStatus;
};
