.rich-text-editor {
  & * {
    font-family: inherit;
  }

  & > [data-floating-ui-focusable]:has(.bn-side-menu) {
    z-index: 0 !important;
  }

  &.white {
    .bn-editor {
      background-color: white;
    }
  }

  .mantine-Menu-dropdown {
    background-color: white;
    border: 1px solid black;
    box-shadow: none;

    .mantine-Menu-item {
      background-color: white;
      color: black;
    }
  }

  .bn-toolbar {
    background-color: white;
    border: 1px solid black;
    box-shadow: none;

    .mantine-Button-root,
    .mantine-ActionIcon-icon {
      color: black;
      background-color: white;
    }
  }

  .bn-editor {
    @apply pl-5 py-1 text-sm xl:text-base;

    color: black;
    background-color: #e8ebed;
    border-radius: 5px;
  }

  .bn-side-menu {
    & .mantine-UnstyledButton-root:not(.mantine-Menu-item):hover {
      background-color: white;
    }
  }
}

.rich-text-editor a {
  color: blue !important;
}

.non-editable-content {
  font-family: inherit;
  font-size: inherit;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.non-editable-content .bn-block-group {
  display: block;
  margin: 0;
  padding: 0;
}

.non-editable-content .bn-block-outer {
  display: block;
  position: relative;
  margin: 0.25rem 0;
}

.non-editable-content .bn-block {
  display: block;
}

.non-editable-content .bn-block-content[data-content-type='numberedListItem'] {
  display: flex;
  padding-left: 1.5rem;
  position: relative;
}

.non-editable-content
  .bn-block-content[data-content-type='numberedListItem']::before {
  content: attr(data-index) '.';
  position: absolute;
  left: 0;
  font-weight: normal;
  color: inherit;
}
