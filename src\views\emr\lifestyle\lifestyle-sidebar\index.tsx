import FormModals from './FormModals';
import NavigationTabs from './NavigationTabs';
import NotesPanel from './NotesPanel';
import PatientCard from './PatientCard';
import './styles.scss';

export default function LifestyleSidebar() {
  return (
    <div className="lifestyle-sidebar h-full bg-white flex flex-col rounded-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="p-2 pb-0">
        <div className="font-semibold">Now Consulting</div>
      </div>

      {/* Patient Information */}
      <div className="p-2 border-b border-gray-200">
        <PatientCard />
      </div>

      {/* Navigation Menu */}
      <div className="flex-1 overflow-y-auto min-h-0">
        <NavigationTabs />
      </div>

      {/* Notes Section */}
      <div className="flex-shrink-0">
        <NotesPanel />
      </div>

      {/* Action Buttons */}
      <div className="p-2 space-y-2 border-t border-gray-200">
        Add Record
        <FormModals />
        {/* <AmbientListening /> */}
      </div>
    </div>
  );
}
