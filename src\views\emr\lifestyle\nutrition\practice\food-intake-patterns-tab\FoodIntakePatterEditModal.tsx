import React, { memo } from 'react';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import AppModal from '@/core/components/app-modal';
import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AccordionTitle from '../../../shared/AccordionTitle';

import FoodIntakePatternModal from './FoodIntakePatternModal';

type Props = {
  open: boolean;
  onClose: () => void;
  formFields: QuestionnaireResponse | null;
  onFinalize: (data: QuestionnaireResponse | null) => void;
};

const FoodIntakePatterEditModal = ({
  open,
  onClose,
  formFields,
  onFinalize,
}: Props) => {
  return (
    <AppModal
      open={open}
      onClose={onClose}
      title="Edit Record"
      classes={{
        root: 'w-[60vw] h-[80vh] flex flex-col min-h-0',
        body: 'flex-1 h-full flex flex-col min-h-0 !p-0',
        header: '!hidden',
      }}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center w-full h-10 p-base bg-[#B4E5FE] rounded-base">
          <AccordionTitle
            expand={true}
            doctorName={formFields?.doctorName}
            finalised={formFields?.status === LifestyleRecordStatus.FINALIZED}
            onExpand={onClose}
            onFinalise={() => {
              onFinalize?.(formFields);
            }}
            open={open}
            stepper={['Practice', 'Food Intake Patterns']}
            designation={formFields?.doctor?.designation}
            department={formFields?.doctor?.department}
            date={formFields?.created_on}
          />
        </div>
        <div className="h-[calc(100%-2.5rem)] w-full p-base">
          <FoodIntakePatternModal
            patientData={formFields}
            mode={LifestyleMode.VIEW}
            onAfterSubmit={() => {
              onClose();
            }}
          />
        </div>
      </div>
    </AppModal>
  );
};

export default memo(FoodIntakePatterEditModal);
