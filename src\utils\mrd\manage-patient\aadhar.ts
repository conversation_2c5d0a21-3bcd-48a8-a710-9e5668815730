import { isValidNumber } from 'aadhaar-validator';

export const formatAadhar = (value: string) => {
  const digits = value.replace(/\D/g, '');
  return digits.match(/.{1,4}/g)?.join('-') ?? digits;
};

export const aadharValidationYup = (val?: string): boolean => {
  const sanitizedValue = val?.replace(/-/g, '');

  if (sanitizedValue?.length !== 12) {
    return false;
  }
  if (!/^\d{12}$/.test(sanitizedValue)) {
    return false;
  }

  if (/^(\d)\1{11}$/.test(sanitizedValue)) {
    return false;
  }

  if (!isValidNumber(sanitizedValue)) {
    return false;
  }
  return true;
};
