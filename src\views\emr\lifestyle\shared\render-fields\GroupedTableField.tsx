import React, { useCallback, useState, useEffect, useMemo } from 'react';

import { Controller, useFieldArray } from 'react-hook-form';

import { Box, Typography } from '@mui/material';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import AppSelect from '@/core/components/app-select';
import AppTextField from '@/core/components/app-text-field';
import AppTimeRange from '@/core/components/app-time-range';
import DeleteModal from '@/core/components/delete-modal';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { GroupedTableField as GroupedTableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

export const GroupedTableField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
}) => {
  const tableField = field as GroupedTableFieldType;
  const [hasInitialized, setHasInitialized] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<{
    groupIndex: number;
    rowIndex: number;
    rowData: any;
  } | null>(null);

  const { fields, append, replace } = useFieldArray({
    control,
    name: name,
  });

  const addRow = useCallback(
    (groupIndex: number) => {
      const newRow: Record<string, any> = {};

      tableField.headers.forEach((header) => {
        if (header.type === 'time_range') {
          newRow[header.id] = { from: '', to: '' };
        } else if (header.type === 'select') {
          newRow[header.id] = '';
        } else {
          newRow[header.id] = '';
        }
      });

      const updatedFields = fields.map((group: any, index) => {
        if (index === groupIndex) {
          const originalMealGroup = tableField.mealGroups?.find(
            (mealGroup) => mealGroup.id === group.id
          );

          return {
            id: originalMealGroup?.id || group.id,
            label: originalMealGroup?.label || group.label,
            rows: [...(group.rows || []), newRow],
          };
        }
        return group;
      });

      replace(updatedFields);
    },
    [fields, tableField.headers, tableField.mealGroups, replace]
  );

  const handleDeleteClick = useCallback(
    (groupIndex: number, rowIndex: number) => {
      const group = fields[groupIndex] as any;
      const rowData = group.rows[rowIndex];
      setRowToDelete({ groupIndex, rowIndex, rowData });
      setDeleteModalOpen(true);
    },
    [fields]
  );

  const handleConfirmDelete = useCallback(() => {
    if (rowToDelete !== null) {
      const updatedFields = fields.map((group: any, index) => {
        if (index === rowToDelete.groupIndex) {
          const originalMealGroup = tableField.mealGroups?.find(
            (mealGroup) => mealGroup.id === group.id
          );

          const updatedRows = group.rows.filter(
            (_: any, rowIndex: number) => rowIndex !== rowToDelete.rowIndex
          );

          return {
            id: originalMealGroup?.id || group.id,
            label: originalMealGroup?.label || group.label,
            rows: updatedRows,
          };
        }
        return group;
      });

      replace(updatedFields);
      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  }, [rowToDelete, fields, tableField.mealGroups, replace]);

  const handleCancelDelete = useCallback(() => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  }, []);

  const getRowDisplayName = useCallback(() => {
    if (!rowToDelete?.rowData) return 'this entry';
    return rowToDelete.rowData.food_item || 'this entry';
  }, [rowToDelete]);

  const { headers, rows } = useMemo(() => {
    // Filter out icon column in readonly mode and adjust headers
    const filteredHeaders = readonly
      ? tableField.headers.filter((header) => header.type !== 'icon')
      : tableField.headers;

    const tableHeaders: HeaderV2[] = readonly
      ? [
          {
            key: 'meal',
            header: 'Meal',
            cellProps: { align: 'center' as const },
          },
          ...filteredHeaders.map((header) => ({
            key: header.id,
            header: header.label,
            cellProps: { align: 'center' as const },
          })),
        ]
      : tableField.headers.map((header) => ({
          key: header.id,
          header: header.label,
          cellProps: { align: 'center' as const },
        }));

    const tableRows: RowV2[] = [];

    if (readonly) {
      fields.forEach((group: any, groupIndex) => {
        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableRow['meal'] = {
              value: <Typography variant="body2">{group.label}</Typography>,
              cellProps: { align: 'center' as const },
            };

            filteredHeaders.forEach((header) => {
              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        return (
                          <AppSelect
                            {...controllerField}
                            value={controllerField.value || ''}
                            options={
                              Array.isArray(header.options)
                                ? header.options.map((option) => ({
                                    label: option,
                                    value: option,
                                  }))
                                : []
                            }
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            type="number"
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: { min: header.min || 0 },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: { align: 'center' as const },
              };
            });

            tableRows.push(tableRow);
          });
        }
      });
    } else {
      // Edit mode: Show group headers with add buttons
      fields.forEach((group: any, groupIndex) => {
        const groupHeaderRow: RowV2 = {
          key: `group-${group.id}`,
        };

        tableField.headers.forEach((header, index) => {
          if (index === 0) {
            groupHeaderRow[header.id] = {
              value: (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                  }}
                >
                  <Typography variant="h6" fontWeight="medium">
                    {group.label}
                  </Typography>
                  <AppIconButton
                    onClick={() => addRow(groupIndex)}
                    variant="outlined"
                    color="info"
                    sx={{ borderRadius: 1, px: 2 }}
                  >
                    <AppIcon icon="humbleicons:plus" />
                  </AppIconButton>
                </Box>
              ),
              cellProps: {
                colSpan: tableField.headers.length,
                sx: {
                  backgroundColor: '#f9fafb',
                  borderBottom: '2px solid #e5e7eb',
                  py: '5px !important',
                },
              },
            };
          } else {
            groupHeaderRow[header.id] = {
              value: null,
              returnNullForEmpty: true,
            };
          }
        });

        tableRows.push(groupHeaderRow);

        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableField.headers.forEach((header) => {
              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        return (
                          <AppSelect
                            {...controllerField}
                            value={controllerField.value || ''}
                            options={
                              Array.isArray(header.options)
                                ? header.options.map((option: string) => ({
                                    label: option,
                                    value: option,
                                  }))
                                : []
                            }
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            type="number"
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: { min: header.min || 0 },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'icon') {
                        return (
                          <div>
                            <AppIconButton
                              onClick={() =>
                                handleDeleteClick(groupIndex, rowIndex)
                              }
                              variant="outlined"
                              color="error"
                            >
                              <AppIcon icon="ic:round-close" />
                            </AppIconButton>
                          </div>
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: { align: 'center' as const },
              };
            });

            tableRows.push(tableRow);
          });
        } else {
          tableRows.push({
            key: `${group.id}-empty`,
            ...tableField.headers.reduce(
              (acc, header, index) => {
                if (index === 0) {
                  acc[header.id] = {
                    value: (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        fontStyle="italic"
                      >
                        No entries for {group.label}
                      </Typography>
                    ),
                    cellProps: {
                      colSpan: tableField.headers.length,
                      align: 'center' as const,
                      sx: { py: 2 },
                    },
                  };
                } else {
                  acc[header.id] = {
                    value: null,
                    returnNullForEmpty: true,
                  };
                }
                return acc;
              },
              {} as Record<string, any>
            ),
          });
        }
      });
    }

    return { headers: tableHeaders, rows: tableRows };
  }, [fields, tableField, name, control, readonly, addRow, handleDeleteClick]);

  useEffect(() => {
    if (!hasInitialized) {
      if (fields.length === 0 && tableField.mealGroups) {
        tableField.mealGroups.forEach((group) => {
          append({
            id: group.id,
            label: group.label,
            rows: group.defaultRows || [],
          });
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasInitialized, tableField.mealGroups, append]);

  return (
    <div className="space-y-6">
      <TableV2
        headers={headers}
        rows={rows}
        tableContainerProps={{
          sx: {
            '& .MuiTableCell-root': {
              padding: '12px 16px',
            },
            ...(readonly && {
              '& .MuiTableHead-root .MuiTableCell-root': {
                backgroundColor: '#64707D',
              },
            }),
          },
        }}
        noDataMessage={
          <Typography variant="body2" color="text.secondary">
            No data available
          </Typography>
        }
      />

      <DeleteModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onDelete={handleConfirmDelete}
        confirmationMessage={`Are you sure you want to delete ${getRowDisplayName()}?`}
      />
    </div>
  );
};
