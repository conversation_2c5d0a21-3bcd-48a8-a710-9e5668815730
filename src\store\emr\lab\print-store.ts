import { create } from 'zustand';

import { TestResultItem } from '@/types/emr/lab';

type PrintState = {
  printItem: TestResultItem | null;
  printOpen: boolean;
};

type PrintAction = {
  setPrintItem: (item: TestResultItem | null) => void;
  onExpand: (item: TestResultItem) => void;
  onClose: () => void;
  setPrintOpen: (printOpen: boolean) => void;
};

type PrintStore = PrintState & PrintAction;

const initialValue = {
  printItem: null,
  printOpen: false,
};

export const useLabPrintStore = create<PrintStore>((set) => ({
  ...initialValue,
  setPrintItem: (item) => set({ printItem: item }),

  setPrintOpen: (printOpen) => set({ printOpen }),
  onExpand: (item) => set({ printItem: item }),
  onClose: () => set({ printItem: null }),
}));
