'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { toast } from 'sonner';

import type { QueueCardProps } from '@/lib/QueueCard';

import { useBroadcastChannel } from '@/hooks/use-broadcast-channel';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { usePatientInfoStore } from '@/store/emr/patient-info';
import { useRecentSearchStore } from '@/store/searchHistoryStore';
import { useUserStore } from '@/store/userStore';

import { getPatientById as getPatientByIdMRD } from '@/query/mrd/manage-patient/manage';
import {
  getPatientQueue,
  reorderQueue,
  updateLastConsultedDate,
  updateQueueItemById,
} from '@/query/patient';

import {
  broadCastActions,
  PatientUpdatedMessage,
} from '@/constants/broadcast-channel';
import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';

import PatientQueue from '@/views/emr/patient-info/PatientQueue';
import PatientSearch from '@/views/emr/patient-info/PatientSearch';

import { PatientInfoPage as PermissionWrapper } from '@/components/permission/protected-page';

import { PatientI } from '@/types';
import { Patient as MRDPatient } from '@/types/mrd/manage-patient/patient';

import './styles.scss';

import ConfirmationModal from '@/views/emr/patient-info/ConfirmationModal';

function mapMrdPatientToEmrPatientI(mrd: MRDPatient): PatientI {
  // Ensure all fields are mapped and types are consistent
  return {
    id: mrd.id || '',
    name: mrd.name || '',
    sex: mrd.sex || '',
    age: String(mrd.age ?? ''),
    maritalStatus: mrd.maritalStatus || '',
    dob:
      typeof mrd.dob === 'string'
        ? mrd.dob
        : mrd.dob instanceof Date
          ? mrd.dob.toISOString()
          : '',
    height:
      mrd.height !== undefined && mrd.height !== null ? String(mrd.height) : '',
    weight:
      mrd.weight !== undefined && mrd.weight !== null ? String(mrd.weight) : '',
    address: mrd.address || '',
    aadhar: mrd.aadhar || '',
    abha: mrd.abha || '',
    contact: {
      phone: mrd.contact?.phone || '',
      email: mrd.contact?.email || '',
    },
    insurance: {
      provider: mrd.insurance?.provider || '',
      id: mrd.insurance?.id || '',
    },
    last_consultation_date: mrd.last_consultation_date || null,
    vitals: [],
    created_on: '',
    updated_on: '',
    _rid: '',
    _self: '',
    _etag: '',
    _attachments: '',
    _ts: 0,
  };
}

const PatientInfoContent = () => {
  const {
    setPatient,
    patient,
    isPatientFromQueue,
    setIsPatientFromQueue,
    isActivePatient,
    updateIsActivePatient,
  } = useCurrentPatientStore();
  const { addSearch } = useRecentSearchStore();
  const [isConfirming, setIsConfirming] = useState(false);
  const {
    selectPatient,
    patient: searchPatient,
    setPatient: setSearchPatient,
    getPatientVitals,
  } = usePatientInfoStore();
  const {
    data: userData,
    setQueue,
    appointments,
    currentAppointment,
    queueFilter,
    moveAppointmentToCurrent,
    moveAppointmentToDone,
    moveAppointmentToCancelled,
    setQueueFilter,
    moveCurrentAppointmentToQueue,
    setAppointments,
  } = useUserStore();

  // Add state for pagination and search
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 6,
    searchQuery: '',
    patientId: '',
    totalItems: 0,
    totalPages: 1,
  });
  const [loadPatientQueueStatus, setLoadPatientQueueStatus] = useState<
    'idle' | 'loading' | 'succeeded' | 'failed'
  >('idle');

  // Load patient queue with pagination and search
  const loadPatientQueue = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoadPatientQueueStatus('loading');

      const result = await getPatientQueue(userData.id, queueFilter.date, {
        page: pagination.page,
        pageSize: pagination.pageSize,
        searchQuery: pagination.searchQuery.trim(), // Trim search query before API call
        status: ['Consultation-Arrived', 'Booked-Booked', 'Booked-Arrived'],
      });

      if (result) {
        setQueue(result.data);
        setPagination((prev) => ({
          ...prev,
          totalItems: result.totalItems,
          totalPages: result.totalPages,
        }));
      }
      setLoadPatientQueueStatus('succeeded');
    } catch (error) {
      console.error('Failed to load patient queue:', error);
      setLoadPatientQueueStatus('failed');
      toast.error('Failed to load patient queue');
    }
  }, [
    userData?.id,
    queueFilter.date,
    pagination.page,
    pagination.pageSize,
    pagination.searchQuery,
    setQueue,
  ]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  // Handle page size change
  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination((prev) => ({ ...prev, pageSize, page: 1 }));
  }, []);

  // Handle search
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const searchQuery = e.target.value;
    setPagination((prev) => ({ ...prev, searchQuery, page: 1 }));
  }, []);

  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );

  const [, updateState] = useState({});
  const forceUpdate = useCallback(() => updateState({}), []);

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showMoveToQueueModal, setShowMoveToQueueModal] = useState(false);
  const [pendingPatientAction, setPendingPatientAction] = useState<{
    currentAppointment: QueueCardProps;
    newPatient: QueueCardProps | null;
  } | null>(null);
  const [pendingQueueAction, setPendingQueueAction] =
    useState<QueueCardProps | null>(null);
  const [isManualSelection, setIsManualSelection] = useState(false);

  const handleSelectPatientFromQueue = useCallback(
    async (item: QueueCardProps | null) => {
      // If clicking the same patient, deselect
      if (item?.queueId === selectedPatientId) {
        setSearchPatient(null);
        setPatient(null);
        setSelectedPatientId(null);
        setIsPatientFromQueue(false);
        setIsManualSelection(false);
        return;
      }

      // If clicking a different patient or a new selection
      if (item) {
        // Update the selected state
        setSelectedPatientId(item.queueId);
        setIsPatientFromQueue(true);
        setIsManualSelection(false);

        // Update the patient data in the search section
        const patientI = item.patient as unknown as PatientI;
        // setSearchPatient(patientI);

        try {
          await selectPatient(item.patient.id);
          setPatient(patientI);
        } catch (error) {
          console.error('Error selecting patient:', error);
          // Revert selection on error
          setSelectedPatientId(null);
          setIsPatientFromQueue(false);
          setIsManualSelection(false);
        }
      } else {
        // Clear selection if null is passed
        setSearchPatient(null);
        setPatient(null);
        setSelectedPatientId(null);
        setIsPatientFromQueue(false);
        setIsManualSelection(false);
      }
    },
    [selectPatient, setPatient, setSearchPatient, selectedPatientId]
  );

  const handleSelectChange = useCallback(
    async (data: PatientI | null) => {
      if (data?.id) {
        setSearchPatient(data);
        addSearch('emr', data);
        await selectPatient(data.id);
        // Set isPatientFromQueue to false since this is a manual selection from search
        setIsPatientFromQueue(false);
        // Track that this is a manual selection
        setIsManualSelection(true);
        // Set the selected patient when searching
        const foundAppointment = appointments.find(
          (appt) => appt.patientId === data.id
        );
        if (foundAppointment) {
          setPatient(foundAppointment.patient as unknown as PatientI);
        }
      } else {
        if (currentAppointment?.patient?.id === patient?.id) {
          moveCurrentAppointmentToQueue();
        }
        setSearchPatient(null);
        setPatient(null);
        setIsPatientFromQueue(false);
        setIsManualSelection(false);
      }
    },
    [
      addSearch,
      appointments,
      currentAppointment?.patient?.id,
      moveCurrentAppointmentToQueue,
      patient?.id,
      selectPatient,
      setIsPatientFromQueue,
      setPatient,
      setSearchPatient,
    ]
  );

  // Actual implementation of moving to queue
  const executeMoveToQueue = useCallback(
    async (item: QueueCardProps) => {
      if (!item) return;

      try {
        moveCurrentAppointmentToQueue();
        await updateQueueItemById({
          queueId: item.queueId,
          payload: {
            time: item.time,
            status: AppointmentStatus.Booked,
            queuePosition: item.queuePosition,
            patientStatus: item.patientStatus || PatientStatus.Booked,
          },
        });
      } catch (err) {
        console.error(err);
        toast.error('Moving appointment to queue failed');
      }
    },
    [moveCurrentAppointmentToQueue]
  );

  // Shows confirmation modal before moving to queue
  const handleMoveAppointmentToQueue = useCallback(
    async (item?: QueueCardProps) => {
      if (!item) return;
      setPendingQueueAction(item);
      setShowMoveToQueueModal(true);
    },
    []
  );

  // Confirmed move to queue action
  const confirmMoveToQueue = useCallback(async () => {
    if (!pendingQueueAction) return;
    setShowMoveToQueueModal(false);
    await executeMoveToQueue(pendingQueueAction);
  }, [handleMoveAppointmentToQueue, pendingQueueAction]);

  const handleMoveAppointmentToConsulting = useCallback(
    async (item?: QueueCardProps, disableSelectChange = false) => {
      setIsPatientFromQueue(false);
      try {
        if (currentAppointment && item) {
          setPendingPatientAction({
            currentAppointment: currentAppointment as QueueCardProps,
            newPatient: item,
          });
          setShowConfirmModal(true);
          return;
        }

        const nextItem = item || appointments[0];
        if (!nextItem) return;

        // Update the appointment status to consulting
        await updateQueueItemById({
          queueId: nextItem.queueId,
          payload: {
            time: nextItem.time,
            status: AppointmentStatus.Consultation,
            queuePosition: nextItem.queuePosition,
            patientStatus: PatientStatus.Arrived,
          },
        });

        // Update the local state - this will trigger a re-render
        moveAppointmentToCurrent(nextItem.queueId);

        if (nextItem?.patient) {
          if (!disableSelectChange) {
            await selectPatient(nextItem.patient.id);
            const patientI = nextItem.patient as unknown as PatientI;
            // Make sure to update both patient states
            setPatient(patientI);
            setSelectedPatientId(nextItem.queueId);

            // Force a re-render to ensure the active state updates
            forceUpdate();
          }
        }
      } catch (err) {
        console.error('err', err);
        moveCurrentAppointmentToQueue();
        toast.error('Moving appointment to consulting failed');
      }
    },
    [
      setIsPatientFromQueue,
      currentAppointment,
      appointments,
      moveAppointmentToCurrent,
      selectPatient,
      setPatient,
      setSearchPatient,
      forceUpdate,
      moveCurrentAppointmentToQueue,
    ]
  );

  const handleMarkAsConsulted = useCallback(
    async (item: QueueCardProps) => {
      try {
        if (!currentAppointment) {
          return;
        }

        // Clear the current patient state
        if (currentAppointment.patient?.id === item.patient?.id) {
          setPatient(null);
          setSelectedPatientId(null);
          setIsPatientFromQueue(false);
          setIsManualSelection(false);
        }

        // Update the appointment status
        await updateQueueItemById({
          queueId: item.queueId,
          payload: {
            time: item.time,
            status: AppointmentStatus.Done,
            queuePosition: item.queuePosition,
            patientStatus: item.patientStatus || PatientStatus.Booked,
          },
        });

        // Move the appointment to done in the store
        moveAppointmentToDone(item.queueId);

        // Update the last consulted date
        await updateLastConsultedDate(
          item?.patient?.id,
          new Date().toISOString()
        );

        toast.success('Appointment marked as done');
        setShowConfirmModal(false);
      } catch (err) {
        console.error(err);
        toast.error('Moving appointment to done failed');
      }
    },
    [
      currentAppointment,
      moveAppointmentToDone,
      setIsPatientFromQueue,
      setPatient,
      setSearchPatient,
    ]
  );
  useEffect(() => {
    updateIsActivePatient({
      currentAppointmentPatientId: currentAppointment?.patient?.id,
      searchPatientId: searchPatient?.id,
      patientId: patient?.id,
      isPatientFromQueue: isPatientFromQueue,
    });
  }, [
    currentAppointment?.patient?.id,
    searchPatient?.id,
    patient?.id,
    isPatientFromQueue,
    updateIsActivePatient,
  ]);

  const togglePatientSelection = useCallback(async () => {
    if (isActivePatient) {
      // If already active, deselect
      setPatient(null);
      setSearchPatient(null);
      setSelectedPatientId(null);
      setIsPatientFromQueue(false);
      setIsManualSelection(false);
      return;
    }

    if (searchPatient) {
      // Handle manual selection while a consultation is active
      if (
        isManualSelection &&
        currentAppointment &&
        currentAppointment.patient?.id !== searchPatient.id
      ) {
        setPendingPatientAction({
          currentAppointment: currentAppointment as QueueCardProps,
          newPatient: null, // No new patient to move to consultation
        });
        setShowConfirmModal(true);
        setIsManualSelection(false); // Reset after action
        return;
      }

      setPatient(searchPatient);

      // If the patient is from queue, move them to consulting
      if (isPatientFromQueue) {
        const queueItem = appointments.find(
          (appt) => appt.patient?.id === searchPatient.id
        );

        if (queueItem) {
          if (currentAppointment) {
            setShowConfirmModal(true);
            setPendingPatientAction({
              currentAppointment: currentAppointment as QueueCardProps,
              newPatient: queueItem as QueueCardProps,
            });
            return;
          }

          try {
            await handleMoveAppointmentToConsulting(
              queueItem as QueueCardProps,
              true
            );
            setIsPatientFromQueue(false);
            setIsManualSelection(false);
          } catch (error) {
            console.error('Error moving patient to consulting:', error);
            toast.error('Failed to start consultation');
            return;
          }
        }
      } else {
        // This case handles selecting a patient from search when no consultation is active
        setPatient(searchPatient);
      }
    }
  }, [
    isActivePatient,
    searchPatient,
    setPatient,
    setSearchPatient,
    setIsPatientFromQueue,
    isPatientFromQueue,
    appointments,
    currentAppointment,
    handleMoveAppointmentToConsulting,
    isManualSelection,
  ]);

  const handleConfirmAction = useCallback(async () => {
    if (!pendingPatientAction) return;
    setIsConfirming(true);

    try {
      // Always mark the current appointment as consulted
      await handleMarkAsConsulted(pendingPatientAction.currentAppointment);

      const newPatientAction = pendingPatientAction.newPatient;

      // If there is a new patient, move them to consultation
      if (newPatientAction) {
        await updateQueueItemById({
          queueId: newPatientAction.queueId,
          payload: {
            time: newPatientAction.time,
            status: AppointmentStatus.Consultation,
            queuePosition: newPatientAction.queuePosition,
            patientStatus: PatientStatus.Arrived,
          },
        });

        moveAppointmentToCurrent(newPatientAction.queueId);

        if (newPatientAction?.patient) {
          setSelectedPatientId(null);
          const patientI = newPatientAction.patient as unknown as PatientI;
          setPatient(patientI);
          setIsPatientFromQueue(false);
          setIsManualSelection(false);
          forceUpdate();
        }
      } else {
        // If there's no new patient, just end the consultation.
        // The `searchPatient` is the manually selected one, so we set it as the active patient.
        if (searchPatient) {
          setPatient(searchPatient);
        }
        setSelectedPatientId(null);
      }
    } catch (err) {
      console.error('Failed to confirm action:', err);
      toast.error('An error occurred while switching patients.');
    } finally {
      setIsConfirming(false);
      setShowConfirmModal(false);
      setPendingPatientAction(null);
    }
  }, [
    pendingPatientAction,
    handleMarkAsConsulted,
    moveAppointmentToCurrent,
    selectPatient,
    setPatient,
    setSearchPatient,
    setIsPatientFromQueue,
    forceUpdate,
  ]);

  const handleCancelAction = useCallback(() => {
    setShowConfirmModal(false);
    setShowMoveToQueueModal(false);
    setPendingPatientAction(null);
    setPendingQueueAction(null);
  }, []);

  const handleChangeOrder = async (newQueue: QueueCardProps[]) => {
    try {
      const payload = {
        appointments: newQueue.map((item) => ({
          queueId: item.queueId || item.appointmentId || '',
          time: item.time,
          queuePosition: item.queuePosition,
          status: item.status,
          patientStatus: item.status || PatientStatus.Booked,
        })),
      };

      setAppointments(newQueue);

      await reorderQueue(payload);

      toast.success('Patient queue reordered');
    } catch (err) {
      console.error(err);
      toast.error('Reordering patient queue failed');
    }
  };

  const handleCancelAppointment = async (item: QueueCardProps) => {
    try {
      moveAppointmentToCancelled(item.queueId);

      await updateQueueItemById({
        queueId: item.queueId,
        payload: {
          time: item.time,
          status: AppointmentStatus.Cancelled,
          queuePosition: item.queuePosition,
          patientStatus: PatientStatus.NoShow,
        },
      });

      toast.success('Appointment cancelled');
    } catch (err) {
      console.error(err);
      toast.error('Cancelling appointment failed');
    }
  };

  const handleClickNextPatient = () => {
    if (currentAppointment) {
      toast.warning('Please mark the current consulation as done');

      return;
    }

    if (!appointments.length) {
      toast.info('No more patients to see. Yaay!!');

      return;
    }

    const sortedAppointments = [...appointments].sort(
      (a, b) => a.queuePosition - b.queuePosition
    );

    const nextAppointment = sortedAppointments[0];

    if (nextAppointment) {
      handleMoveAppointmentToConsulting(nextAppointment);
    }
  };

  const handleSelectConsultationDate = (date: Date) => {
    setQueueFilter({ date: date });
  };

  useBroadcastChannel((data: PatientUpdatedMessage | any) => {
    if (data.type === broadCastActions.CREATE_CONSULTATION) {
      loadPatientQueue();
    } else if (data.type === broadCastActions.UPDATE_VITALS) {
      getPatientVitals(searchPatient?.id);
    } else if (data.type === broadCastActions.PATIENT_UPDATED) {
      // If the updated patient is the active patient, refresh their details
      if ('patientId' in data && patient?.id === data.patientId) {
        selectPatient(data.patientId).then((freshPatient) => {
          setPatient(freshPatient);
        });
      }
    }
  });

  useEffect(() => {
    loadPatientQueue();
  }, [loadPatientQueue]);

  useEffect(() => {
    if (currentAppointment?.patientId) {
      selectPatient(currentAppointment.patientId);
    }
  }, [currentAppointment?.patientId, selectPatient]);

  useEffect(() => {
    getPatientVitals(searchPatient?.id);
  }, [getPatientVitals, searchPatient]);

  useEffect(() => {
    const setCurrentAppointmentPatient = async () => {
      if (currentAppointment?.patient && !searchPatient && !selectedPatientId) {
        try {
          const patientData = currentAppointment.patient as unknown as PatientI;
          await selectPatient(patientData.id);
          setPatient(patientData);
          setSearchPatient(patientData);
          setSelectedPatientId(currentAppointment.queueId);
        } catch (error) {
          console.error('Error setting current appointment patient:', error);
        }
      }
    };
    setCurrentAppointmentPatient();
  }, [
    currentAppointment,
    searchPatient,
    selectedPatientId,
    selectPatient,
    setPatient,
    setSearchPatient,
  ]);

  useEffect(() => {
    const refreshActivePatient = async () => {
      if (patient?.id) {
        const latest = await getPatientByIdMRD(patient.id);
        if (latest) {
          const mapped = mapMrdPatientToEmrPatientI(latest);
          setPatient(mapped);
          // Also update the searched patient if it matches
          if (searchPatient?.id === mapped.id) {
            setSearchPatient(mapped);
          }
        }
      }
    };
    refreshActivePatient();
  }, [patient?.id]);

  const handleClosePatient = useCallback(() => {
    if (currentAppointment?.patient) {
      setSearchPatient(currentAppointment.patient as PatientI);
      setPatient(currentAppointment.patient as PatientI);
      setSelectedPatientId(currentAppointment.queueId);
    } else {
      setSearchPatient(null);
      setPatient(null);
      setSelectedPatientId(null);
    }
    setIsPatientFromQueue(false);
    setIsManualSelection(false);
  }, [currentAppointment, setSearchPatient, setPatient]);

  return (
    <div className="h-full flex flex-col w-full">
      <div className="flex flex-1 h-full rounded-xl gap-base w-full">
        <PatientSearch
          handleSelectChange={handleSelectChange}
          isActivePatient={isActivePatient}
          togglePatientSelection={togglePatientSelection}
          onClosePatient={handleClosePatient}
          hasSelectedPatient={isPatientFromQueue || isManualSelection}
        />
        <PatientQueue
          handleClickNextPatient={handleClickNextPatient}
          handleMarkAsConsulted={handleMarkAsConsulted}
          handleMoveAppointmentToQueue={handleMoveAppointmentToQueue}
          handleMoveAppointmentToConsulting={handleMoveAppointmentToConsulting}
          handleCancelAppointment={handleCancelAppointment}
          handleChangeOrder={handleChangeOrder}
          handleSelectConsultationDate={handleSelectConsultationDate}
          loadPatientQueueStatus={loadPatientQueueStatus}
          onSelectPatient={handleSelectPatientFromQueue}
          selectedPatientId={selectedPatientId}
          onSearch={handleSearch}
          searchQuery={pagination.searchQuery}
          totalItems={pagination.totalItems}
          currentPage={pagination.page}
          pageSize={pagination.pageSize}
          totalPages={pagination.totalPages}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          isPatientFromQueue={isPatientFromQueue}
        />

        {/* End Consultation Confirmation Modal */}
        {showConfirmModal && pendingPatientAction && (
          <ConfirmationModal
            open={showConfirmModal}
            onClose={handleCancelAction}
            onConfirm={handleConfirmAction}
            patientName={pendingPatientAction.currentAppointment.patient?.name}
            type="end-consultation"
            isLoading={isConfirming}
          />
        )}

        {/* Move to Queue Confirmation Modal */}
        {showMoveToQueueModal && pendingQueueAction && (
          <ConfirmationModal
            open={showMoveToQueueModal}
            onClose={handleCancelAction}
            onConfirm={confirmMoveToQueue}
            patientName={pendingQueueAction.patient?.name}
            type="move-to-queue"
          />
        )}
      </div>
    </div>
  );
};

// Main component with permission check
const PatientInfoPage = memo(function PatientInfoPage() {
  return (
    <PermissionWrapper className="h-full flex flex-col w-full">
      <PatientInfoContent />
    </PermissionWrapper>
  );
});

export default PatientInfoPage;
