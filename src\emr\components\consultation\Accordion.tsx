import React from 'react';

import {
  Accordion as MuiAccordion,
  AccordionSummary,
  AccordionDetails,
  styled,
} from '@mui/material';
import { IoChevronDown } from 'react-icons/io5';

import { cn } from '@/lib/utils';

import ExpandSimpleIcon from '@/assets/svg/ExpandSimpleIcon';
import LockClosedIcon from '@/assets/svg/LockClosedIcon';
import LockOpenIcon from '@/assets/svg/LockOpenIcon';
import PrintIcon from '@/assets/svg/PrintIcon';

import OutlinedIconButton from '../lifestyle/lifestyle-forms/shared/OutlinedIconButton';

const StyledAccordion = styled(MuiAccordion)<{
  width?: string;
  expand?: boolean;
}>(({ expanded, theme, width, expand }) => ({
  width: width || '100%',
  minWidth: '100%',
  backgroundColor: 'transparent',
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  ['& .MuiAccordionSummary-root']: {
    height: 48,
    minHeight: 48,
    padding: '0 12px',
    borderRadius: '6px',
    backgroundColor: expanded ? '#B4E5FE' : '#E6F6FF',

    border: '1px solid #E0E0E0',
    '&.Mui-expanded': {
      minHeight: 48,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
  },

  ['& .MuiAccordionSummary-content']: {
    margin: 0,
    height: '100%',
    minHeight: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: '16px',
    '&.Mui-expanded': {
      margin: 0,
    },
  },

  ['& .MuiAccordionDetails-root']: {
    backgroundColor: 'white',
    padding: 8,
    border: '2px solid #E0E0E0',
    borderTop: 'none',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
  },
}));

interface AccordionProps {
  children: React.ReactNode;
  designation?: string;
  doctorName?: string;
  date?: string;
  open: boolean;
  onToggle?: () => void;
  onFinalise?: () => void;
  onExpand?: () => void;
  onPrint?: () => void;
  showFinaliseButton?: boolean;
  isDownloadPDF?: boolean;
  pdfDocument?: JSX.Element;
  fileName?: string;
  expand?: boolean;
  width?: string;
  className?: string;
  hideFinaliseButton?: boolean;
}

const Accordion: React.FC<AccordionProps> = ({
  children,
  onToggle,
  open,
  date,
  doctorName,
  designation,
  onFinalise,
  onExpand,
  onPrint,
  showFinaliseButton = true,
  expand,
  width,
  className = '',
  hideFinaliseButton = false,
}) => {
  return (
    <StyledAccordion
      className={` h-full ${className}`}
      expanded={open}
      onChange={onToggle}
      width={width}
      expand={expand}
    >
      <AccordionSummary className="gap-1">
        <div className="flex items-center gap-3 text-blue-900 w-full">
          {expand ? (
            <OutlinedIconButton
              size="small"
              showBorder={true}
              sx={{ backgroundColor: 'white !important' }}
            >
              <div className="w-10 h-10 rounded-full bg-transparent flex items-center justify-center text-[#6B7280] font-semibold text-base">
                {doctorName
                  ?.split(' ')
                  .map((n) => n[0])
                  .join('')
                  .toUpperCase()
                  .slice(0, 2) || '--'}
              </div>
            </OutlinedIconButton>
          ) : (
            <OutlinedIconButton
              size="small"
              showBorder={true}
              sx={{ backgroundColor: 'white !important' }}
            >
              <IoChevronDown
                className={cn('transition-transform duration-200 text-base', {
                  'transform -rotate-90': !open,
                })}
              />
            </OutlinedIconButton>
          )}
          <div className="flex flex-row items-center gap-4 w-full">
            <span className="font-archivo font-medium text-xl text-[#001926] leading-none">
              {doctorName}
            </span>
            {designation && (
              <span className="text-[#64707D] text-base font-normal leading-none">
                |
              </span>
            )}
            <span className="text-[#001926] text-base font-normal leading-none">
              {designation}
            </span>
            <span className="text-[#64707D] text-base font-normal leading-none">
              |
            </span>
            <span className="text-[#001926] text-base font-normal leading-none">
              {date}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {!hideFinaliseButton && (
            <OutlinedIconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onFinalise?.();
              }}
              disabled={!showFinaliseButton}
              sx={{ backgroundColor: 'white !important' }}
            >
              {showFinaliseButton ? (
                <LockOpenIcon className="text-sm" />
              ) : (
                <LockClosedIcon className="text-sm" />
              )}
            </OutlinedIconButton>
          )}
          <OutlinedIconButton
            onClick={(e) => {
              e.stopPropagation();
              onPrint?.();
            }}
            showBorder={true}
            sx={{ backgroundColor: 'white !important' }}
          >
            <PrintIcon />
          </OutlinedIconButton>
          <OutlinedIconButton
            onClick={(e) => {
              e.stopPropagation();
              onExpand?.();
            }}
            showBorder={true}
            sx={{ backgroundColor: 'white !important' }}
          >
            <ExpandSimpleIcon className="text-sm" />
          </OutlinedIconButton>
        </div>
      </AccordionSummary>
      <AccordionDetails className="accordion-details">
        {children}
      </AccordionDetails>
    </StyledAccordion>
  );
};

export default Accordion;
