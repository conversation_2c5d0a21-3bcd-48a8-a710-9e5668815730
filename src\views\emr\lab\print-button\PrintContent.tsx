import React, { memo, useMemo, useRef } from 'react';

import { useReactToPrint } from 'react-to-print';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useLabPrintStore } from '@/store/emr/lab/print-store';

import { getLabTestsTableRows } from '@/utils/emr/lab/result';

import { printTableHeader } from '@/constants/emr/lab';

import AppButton from '@/core/components/app-button';
import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import '@/styles/emr/lab/print.css';

import AppSign from './AppSign';
import PatientDetails from './PatientDetails';
import PrintHeader from './PrintHeader';
import TableFooter from './TableFooter';

const PrintContent = () => {
  const { printItem, setPrintOpen, printOpen } = useLabPrintStore();
  const contentRef = useRef<HTMLDivElement>(null);
  const { customiseEmrData } = useCustomiseEmrStore();
  const mostRecentData = customiseEmrData?.length
    ? [...customiseEmrData].sort(
        (a, b) =>
          new Date(b.updated_on || '').getTime() -
          new Date(a.updated_on || '').getTime()
      )[0]
    : null;

  const handlePrint = useReactToPrint({
    contentRef,
    documentTitle: 'lab tests',
    onBeforePrint: async () => setPrintOpen(true),
    onAfterPrint: () => setPrintOpen(false),
    onPrintError: () => setPrintOpen(false),
  });

  const rows = useMemo<Row[]>(
    () =>
      getLabTestsTableRows({
        testResult: printItem ? [printItem] : [],
      }),
    [printItem]
  );

  return (
    <div
      ref={contentRef}
      className="w-full h-full max-h-full flex flex-col print-content-print"
    >
      <div className="w-full h-full flex flex-col p-base">
        <PrintHeader />
        <div className="w-full flex justify-center p-base font-medium">
          Lab Tests
        </div>
        <PatientDetails />
        <div className="flex-1 flex flex-col gap-base pb-base w-full justify-between overflow-hidden">
          <Table
            headers={printTableHeader}
            rows={rows}
            stickyHeader
            noDataMessage="No test results found"
            tableContainerProps={{
              sx: {
                maxHeight: '100%',
                '& th': {
                  whiteSpace: 'nowrap',
                  fontSize: '0.875rem',
                  padding: '8px 16px',
                  backgroundColor: '#64707D',
                },
              },
            }}
          />
          <TableFooter />
        </div>
      </div>
      <div className="w-full flex flex-col border-t">
        <div className="print-absolute-bottom" style={{ marginTop: '6px' }}>
          <AppSign />
        </div>
        <div className="w-full flex justify-end p-base">
          {!printOpen && <AppButton onClick={handlePrint}>Print</AppButton>}
        </div>
      </div>
    </div>
  );
};

export default memo(PrintContent);
