import React, { FC, memo, useCallback } from 'react';

import { BiEditAlt } from 'react-icons/bi';
import { MdOutlineSave } from 'react-icons/md';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import AppButton from '@/core/components/app-button';
import Loader from '@/core/components/app-loaders/Loader';

type Props = {
  children: React.ReactNode;
  loading?: boolean;
  onSubmit?: () => void;
  updating?: boolean;
  mode: LifestyleMode;
  onEdit?: () => void;
  finalized: boolean;
};

const ModalWrapper: FC<Props> = ({
  children,
  loading,
  onSubmit,
  updating,
  mode,
  onEdit,
  finalized,
}) => {
  const renderButtons = useCallback(() => {
    if (finalized) return <div />;
    if (mode !== LifestyleMode.VIEW) {
      return (
        <AppButton
          onClick={onSubmit}
          loading={updating}
          endIcon={<MdOutlineSave />}
          sx={{ borderRadius: 3 }}
        >
          Save Record
        </AppButton>
      );
    } else {
      return (
        <AppButton
          onClick={onEdit}
          endIcon={<BiEditAlt />}
          sx={{ borderRadius: 3 }}
        >
          Edit Record
        </AppButton>
      );
    }
  }, [finalized, mode, onEdit, updating]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <Loader />
      </div>
    );
  }

  return (
    <form
      className="h-full w-full flex flex-col max-h-full"
      onSubmit={onSubmit}
    >
      <div className="flex-1 overflow-y-auto min-h-0">{children}</div>
      <div className="flex p-base pb-0 border-t min-h-10">
        {renderButtons()}
      </div>
    </form>
  );
};

export default memo(ModalWrapper);
