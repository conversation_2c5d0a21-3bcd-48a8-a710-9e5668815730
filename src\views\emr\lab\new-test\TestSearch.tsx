import { useState, useEffect, useCallback } from 'react';

import { components } from 'react-select';
import type { SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';

import { useTestStore } from '@/store/emr/lab/reports-store';

import AsyncSearch from '@/core/components/search';
import { LabTestItem } from '@/types/emr/lab';

import CommonSelectDropdown from '../packages/DepartmentDropdown';

interface TestOption {
  value: string;
  label: string;
  test?: LabTestItem;
}

interface TestSearchProps {
  placeholder?: string;
  onChange?: (test: LabTestItem | null) => void;
  pageKey: string;
}

const TestSearch: React.FC<TestSearchProps> = ({
  placeholder = 'Search by Test',
  onChange,
  pageKey,
}) => {
  const { searchTests, setSelectedSearchTest, labDepartments } = useTestStore();

  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<TestOption | null>(null);
  const [options, setOptions] = useState<TestOption[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('ALL');

  const performSearch = useCallback(
    async (searchTerm: string, department: string) => {
      try {
        const tests: LabTestItem[] = await searchTests(searchTerm, department);
        const opts: TestOption[] = tests
          .filter(
            (test): test is LabTestItem & { id: string; name: string } =>
              !!test.id && !!test.name
          )
          .map((test) => ({
            value: test.id,
            label: test.name,
            test,
          }));
        return opts;
      } catch (error) {
        console.error('Error loading test options:', error);
        return [];
      }
    },
    [searchTests]
  );

  const loadOptions = useCallback(
    (inputVal: string, callback: (options: readonly TestOption[]) => void) => {
      performSearch(inputVal, selectedDepartment || '')
        .then(callback)
        .catch(() => callback([]));
    },
    [performSearch, selectedDepartment]
  );

  useEffect(() => {
    if (inputValue.trim() !== '') {
      performSearch(inputValue, selectedDepartment || '')
        .then(setOptions)
        .catch(() => setOptions([]));
    } else {
      setOptions([]);
    }
  }, [inputValue, selectedDepartment, performSearch]);

  const handleChange = (newValue: SingleValue<TestOption>) => {
    const test = newValue ? newValue.test : null;
    if (test) {
      setSelectedSearchTest(test, pageKey);
    }
    onChange?.(test as LabTestItem);
    setSelectedOption(newValue);
    setInputValue('');
    setTimeout(() => setSelectedOption(null), 800);
  };

  const CustomOption = (props: any) => {
    const { test } = props.data;
    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0.5}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              {test.name}
            </Typography>
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  return (
    <>
      <div className="flex-[2]">
        <AsyncSearch<TestOption>
          loadOptions={loadOptions}
          onChange={handleChange}
          placeholder={placeholder}
          defaultOptions={options}
          cacheOptions
          components={{ Option: CustomOption }}
          value={selectedOption}
          inputValue={inputValue}
          onInputChange={(newVal, { action }) => {
            if (action === 'input-change') {
              setInputValue(newVal);
            }
          }}
        />
      </div>
      <div className="flex-[1.2]">
        <CommonSelectDropdown
          name="department"
          value={selectedDepartment}
          placeholder="Select Your Department"
          options={labDepartments}
          onChange={(value) => setSelectedDepartment(value)}
          className="w-full"
          formControlProps={{ sx: { maxWidth: '100%' } }}
        />
      </div>
    </>
  );
};

export default TestSearch;
