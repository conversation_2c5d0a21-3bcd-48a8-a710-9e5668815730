import { throttle } from 'lodash';

import API_CONFIG from '@/core/configs/api';
import { logout } from '@/core/lib/auth/services';

const IDLE_THRESHOLD = 1000 * 60 * 10;
let debounceTimeout: NodeJS.Timeout | null = null;

const handleIdle = () => {
  logout();
};

export const updateLastActivity = () => {
  if (API_CONFIG.NODE_ENV === 'development') return;
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }
  debounceTimeout = setTimeout(() => {
    handleIdle();
  }, IDLE_THRESHOLD);
};

export const throttledUpdateLastActivity = throttle(updateLastActivity, 10000, {
  trailing: false,
});
