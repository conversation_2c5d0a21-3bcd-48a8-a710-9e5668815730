import { PrescriptionItem, PrescriptionPackageType } from '.';

export interface Medicine {
  id: string;
  drugForm: string;
  genericName: string;
  brandName: string;
  strength: string;
  measure: string;
  uom: string;
  nosPerMlOrGm: string;
  mrp: string;
}

export interface DepartmentPackage {
  id: string;
  name: string;
  medicines: PrescriptionItem[];
}

export interface MedicineItem {
  id: string;
  medicineName: string;
  brandName: string;
  strength: string;
  drugForm: string;
  selected: boolean;
  unitOfMeasure: string;
  measure: string;
  cost: number;
}

export interface PrescriptionPackageData {
  id: string;
  name: string;
  type: PrescriptionPackageType;
}
