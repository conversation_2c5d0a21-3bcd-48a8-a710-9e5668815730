import { api } from '@/core/lib/interceptor';

const ABDM_BASE_URL = '/abdm/v0.1/abdm';

export interface AadhaarOtpRequest {
  aadhaar: string;
  txnId?: string;
}

export interface AadhaarOtpResponse {
  txnId: string;
  message: string;
  status: string;
}

export interface AbhaOtpResponse {
  requestId: string;
  message: string;
  status: string;
}

export interface VerifyOtpRequest {
  authMethod: 'AADHAAR_OTP' | 'MOBILE_OTP' | 'DEMOGRAPHICS';
  healthid: string;
  requestId: string;
  transactionId: string;
}

// Send OTP to the ABHA number
export const sendAbhaOtp = async (
  abhaNumber: string
  mobileNum
): Promise<AbhaOtpResponse> => {
  const { data } = await api.post<AbhaOtpResponse>(
    `${ABDM_BASE_URL}/details/by-number`,
    {
      abhaNumber,
    }
  );
  return data;
};

// Send Aadhaar OTP
export const sendAadhaarOtp = async (
  aadhaar: string
): Promise<AadhaarOtpResponse> => {
  const { data } = await api.post<AadhaarOtpResponse>(
    `${ABDM_BASE_URL}/initiate/aadhaar`,
    {
      aadhaar,
    }
  );
  return data;
};

// Verify Aadhaar OTP
export const verifyAadhaarOtp = async (
  aadhaar: string,
  otp: string,
  txnId: string
): Promise<any> => {
  const { data } = await api.post('/abdm/v1/registration/aadhaar/verifyOTP', {
    otp,
    txnId,
  });
  return data;
};

// Verify OTP and get patient details
export const verifyAbhaOtp = async (params: {
  authMethod: 'AADHAAR_OTP' | 'MOBILE_OTP' | 'DEMOGRAPHICS';
  healthid: string;
  requestId: string;
  transactionId: string;
}): Promise<AbhaOtpResponse> => {
  const { data } = await api.post<AbhaOtpResponse>(
    `${ABDM_BASE_URL}/verify-otp-fetch-details`,
    params
  );
  return data;
};
