import { IconButton, styled } from '@mui/material';

import { AppIconButtonProps } from './type';

export const MuiIconButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'variant',
})<AppIconButtonProps>(({ theme }) => ({
  variants: [
    {
      props: { variant: 'outlined' },
      style: {
        border: theme.shape.border,
        borderSize: 1,
        padding: 5,
        borderRadius: '50%',
        borderColor: theme.palette.grey[500],
      },
    },
    {
      props: { size: 'small' },
      style: { height: 20, width: 20, '& svg': { height: 16, width: 16 } },
    },
    {
      props: { size: 'medium' },
      style: { height: 30, width: 30, '& svg': { height: 20, width: 20 } },
    },
    {
      props: { size: 'large' },
      style: { height: 40, width: 40, '& svg': { height: 24, width: 24 } },
    },
  ],
}));
