import React, { useState, useCallback } from 'react';

import { Box, Divider, Typography } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';
import { usePrescriptionPackageStore } from '@/store/emr/prescription/package';

import AppAsyncSearch, {
  AppAsyncSearchOption,
} from '@/core/components/app-async-search';
import { PrescriptionItem } from '@/types/emr/prescription';
import { MedicineItem } from '@/types/emr/prescription/package';

import { CustomMedicineOption } from './styled-component';

interface MedicineOption extends AppAsyncSearchOption {
  medicine: PrescriptionItem;
}

interface PackageMedicineSearchProps {
  placeholder?: string;
}

const PackageMedicineSearch: React.FC<PackageMedicineSearchProps> = ({
  placeholder = 'Search & add medicines',
}) => {
  const { searchMedicines } = usePrescriptionStore();
  const { addMultipleMedicines } = usePrescriptionPackageStore();
  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<MedicineOption | null>(
    null
  );

  const loadOptions = (
    inputValue: string,
    callback: (options: readonly MedicineOption[]) => void
  ) => {
    searchMedicines(inputValue)
      .then((medicines) => {
        const options: MedicineOption[] = medicines
          .filter((medicine) => !!medicine.id)
          .map((medicine) => ({
            value: medicine.id as string,
            label: medicine.BrandName || medicine.GenericName || '',
            medicine: medicine,
          }));

        callback(options);
      })
      .catch((error) => {
        console.error('Error loading medicine options:', error);
        callback([]);
      });
  };

  const handleChange = (newValue: MedicineOption | null) => {
    if (newValue?.medicine) {
      const prescriptionMedicine = newValue.medicine;

      const medicineItem: Omit<MedicineItem, 'selected'> = {
        id: prescriptionMedicine.id || '',
        medicineName:
          prescriptionMedicine.GenericName ||
          prescriptionMedicine.genericName ||
          '',
        brandName:
          prescriptionMedicine.BrandName ||
          prescriptionMedicine.brandName ||
          '',
        strength:
          prescriptionMedicine.Strength || prescriptionMedicine.strength || '',
        drugForm:
          prescriptionMedicine.DrugFormulation ||
          prescriptionMedicine.drugForm ||
          '',
        unitOfMeasure: (prescriptionMedicine.UnitOfMeasure ||
          prescriptionMedicine.unitOfMeasure ||
          '') as string,
        measure:
          prescriptionMedicine.Measure || prescriptionMedicine.measure || '',
        cost: parseFloat(prescriptionMedicine.Cost as string) || 0,
      };

      addMultipleMedicines([medicineItem]);
    }

    setSelectedOption(newValue);
    setInputValue('');
    setTimeout(() => {
      setSelectedOption(null);
    }, 800);
  };

  const renderOption = useCallback((option: MedicineOption) => {
    const { medicine } = option;

    const medicineDetails = [
      { label: medicine.DrugFormulation, flex: 2 },
      { label: medicine.BrandName, flex: 4 },
      { label: medicine.Strength, flex: 4 },
      { label: medicine.Measure, flex: 1 },
      { label: medicine.UnitOfMeasure, flex: 1 },
    ];

    return (
      <Box>
        <CustomMedicineOption display="flex" alignItems="center" px={1} py={0}>
          {medicineDetails.map((detail, index) => (
            <Box
              key={index}
              flex={detail.flex}
              textAlign="left"
              ml={index > 0 ? 1 : 0}
              p={2}
            >
              <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                {detail.label}
              </Typography>
            </Box>
          ))}
        </CustomMedicineOption>
        <Divider sx={{ my: 0 }} />
      </Box>
    );
  }, []);

  return (
    <AppAsyncSearch<MedicineOption>
      loadOptions={loadOptions}
      onChange={handleChange}
      onInputChange={(value) => setInputValue(value)}
      placeholder={placeholder}
      cacheOptions
      renderOption={renderOption}
      value={selectedOption}
      inputValue={inputValue}
    />
  );
};

export default PackageMedicineSearch;
