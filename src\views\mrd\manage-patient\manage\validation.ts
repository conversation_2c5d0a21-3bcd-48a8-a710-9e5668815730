import * as yup from 'yup';

import { aadharValidationYup } from '@/utils/mrd/manage-patient/aadhar';

// Extend yup string type
declare module 'yup' {
  interface StringSchema {
    validDate(message?: string): this;
    aadharNumber(message?: string): this;
  }
}

yup.addMethod(
  yup.string,
  'validDate',
  function (message = 'Invalid date format') {
    return this.test('valid-date', message, function (value) {
      if (!value) return true;
      return !isNaN(Date.parse(value));
    });
  }
);

yup.addMethod(
  yup.string,
  'aadharNumber',
  function (message = 'Invalid aadhar number') {
    return this.test('valid-aadhar', message, function (value) {
      if (!value) return true;
      return aadharValidationYup(value);
    });
  }
);

const generalDetailsSchema = yup.object().shape({
  name: yup.string().required('Required'),
  dob: yup.string().required('Required').validDate('Invalid date format'),
  sex: yup.object().required('Required'),
  height: yup
    .string()
    .test('is-positive', 'Height must be a positive number', (value) => {
      if (!value) return true;
      return Number(value) > 0;
    })
    .nullable(),
  weight: yup
    .string()
    .test('is-positive', 'Weight must be a positive number', (value) => {
      if (!value) return true;
      return Number(value) > 0;
    })
    .nullable(),
  maritalStatus: yup.object().nullable(),

  address: yup.object().shape({
    houseName: yup.string(),
    street: yup.string(),
    city: yup.string().required('Required'),
    district: yup.string(),
    state: yup.object().nullable().optional(),
    country: yup.object().nullable().optional(),
    pin: yup
      .string()
      .matches(/^[0-9]{6}$/, 'PIN must be 6 digits')
      .optional(),
  }),

  contact: yup.object().shape({
    phone: yup
      .string()
      .required('Required')
      .matches(/^[0-9]{10}$/, 'Must be 10 digits'),
    email: yup.string().email('Invalid email address').nullable(),
  }),
});

const verificationSchema = yup.object().shape({
  proof: yup.object().shape({
    type: yup.object().nullable().optional(),
    aadharNumber: yup.string().aadharNumber('Invalid aadhar number').optional(),
    abhaNumber: yup.string().optional(),
    url: yup.mixed().optional(),
  }),
});

const insuranceSchema = yup.object().shape({
  provider: yup.object().nullable().optional(),
  insuranceId: yup.string().optional(),
  url: yup.mixed().optional(),
});

const finalSchema = yup.object().shape({});

export const tabValidationSchemas = {
  0: generalDetailsSchema,
  1: verificationSchema,
  2: insuranceSchema,
  3: finalSchema,
};
