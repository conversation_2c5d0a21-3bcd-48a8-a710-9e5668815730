import { getTimeAgo } from '@/utils/dateUtils/dayUtils';

import { VitalCardAttributes } from '@/components/vitals-card/types';

import { Vitals } from '@/types/mrd/manage-patient/vitals';

class CalculateChanges {
  vital: Vitals | null;
  prevVital: Vitals | null;
  constructor(vital: Vitals | null, prevVital: Vitals | null) {
    this.vital = vital;
    this.prevVital = prevVital;
  }

  isValid() {
    return this.vital && this.prevVital;
  }

  isNumber(vital?: number | string | null) {
    const convertedValue = Number(vital);
    return typeof convertedValue === 'number' && !isNaN(convertedValue);
  }

  differenceInPercentage(
    vital?: number | string | null,
    prevVital?: number | string | null
  ) {
    if (!this.isNumber(Number(vital)) || !this.isNumber(Number(prevVital))) {
      return 0;
    }
    return ((Number(vital) - Number(prevVital)) / Number(prevVital)) * 100;
  }

  differenceInString(
    vital?: number | string | null,
    prevVital?: number | string | null
  ) {
    if (!this.isNumber(Number(vital)) || !this.isNumber(Number(prevVital))) {
      return '0%';
    }
    const percentage = Number(vital) - Number(prevVital);
    return `${percentage.toFixed(2)}%`;
  }

  calculateCreatedAt = (createdAt?: string): string => {
    if (!createdAt) return '';
    return getTimeAgo(createdAt);
  };

  getChangeStatus(
    field: keyof Omit<
      Vitals,
      'id' | 'patientId' | 'createdAt' | 'vitalStatuses' | 'ageGroup'
    >
  ) {
    const change = this.differenceInPercentage(
      this.vital?.[field],
      this.prevVital?.[field]
    );
    if (change > 0) return 'improved';
    if (change < 0) return 'worsened';
    return 'stable';
  }
}

type FormatVitalArgs = {
  vital: Vitals | null;
  prevVital: Vitals | null;
};

const formatVitals = ({
  vital,
  prevVital,
}: FormatVitalArgs): VitalCardAttributes[] => {
  if (!vital) return [];

  const methods = new CalculateChanges(vital, prevVital);

  return [
    {
      label: 'Height',
      value: vital?.height,
      unit: 'cm',
      status: vital?.vitalStatuses?.height ?? 'unknown',
      improvement: methods.getChangeStatus('height'),
      percentage: methods.differenceInString(vital?.height, prevVital?.height),
    },
    {
      label: 'Weight',
      value: vital?.weight,
      unit: 'kg',
      status: vital?.vitalStatuses.weight ?? 'unknown',
      improvement: methods.getChangeStatus('weight'),
      percentage: methods.differenceInString(vital?.weight, prevVital?.weight),
    },
    {
      label: 'BP',
      value:
        vital?.sbp && vital?.dbp
          ? `${vital?.sbp ?? ''}/${vital?.dbp ?? ''}`
          : '',
      status: vital?.vitalStatuses.bp ?? 'unknown',
      improvement: methods.getChangeStatus('bloodPressure'),
      time: methods.calculateCreatedAt(vital?.createdAt),
    },
    {
      label: 'BMI',
      value: vital?.bmi,
      status: vital?.vitalStatuses.bmi ?? 'unknown',
      improvement: methods.getChangeStatus('bmi'),
      percentage: methods.differenceInString(vital?.bmi, prevVital?.bmi),
    },
    {
      label: 'Pulse',
      value: vital?.pulse,
      unit: 'BPM',
      status: vital?.vitalStatuses.pulse ?? 'unknown',
      improvement: methods.getChangeStatus('pulse'),
      time: methods.calculateCreatedAt(vital?.createdAt),
    },
    {
      label: 'RR',
      value: vital?.rr,
      unit: 'Un',
      status: vital?.vitalStatuses?.rr ?? 'unknown',
      improvement: methods.getChangeStatus('rr'),
      time: methods.calculateCreatedAt(vital?.createdAt),
    },
  ];
};

export default formatVitals;
