import { useCallback, useState } from 'react';
import React from 'react';

import { useFormContext } from 'react-hook-form';

import { IconButton } from '@mui/material';
import { IoIosArrowDown, IoIosClose } from 'react-icons/io';

import useIsMobile from '@/hooks/use-mobile-layout';

import {
  DropdownMenu,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import TextInput from '../text-input';

import DropDown from './DropDown';
import MultiLevelTextInput from './DropdownText';
import MobileDropDown from './mobile-drop-down';
import './styles.scss';
import { MultiDropDownProps } from './types';

export default function MultiDropDown({
  options,
  onSelect,
  value,
  name,
  error,
  label,
  fieldClass,
  placeholder = 'Select',
  maxHeight = 'max-h-50',
  disabledInput,
  endDecoration,
  className,
  isMultiLevel = false,
  isLoading,
  loading,
  isClearable = false,
  onClear,
}: MultiDropDownProps) {
  const form = (() => {
    try {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      return useFormContext();
    } catch {
      return undefined;
    }
  })();

  const register = form?.register;
  const watch = form?.watch;
  const setValue = form?.setValue;
  const isMobile = useIsMobile();

  const [selectedOption, setSelectedOption] = useState<string | null>(
    value ?? null
  );
  const [selectedSubOption, setSelectedSubOption] = useState<string | null>(
    null
  );
  const [isOpen, setIsOpen] = useState(!useIsMobile());

  const selectedValue = watch?.(name as string) ?? value ?? '';

  const handleSelect = (
    value: string,
    key?: string,
    isSubOption: boolean = false
  ) => {
    if (isMultiLevel && !isSubOption) {
      setSelectedOption(null);
      setSelectedSubOption(null);
    } else {
      setSelectedOption(value);
      if (isSubOption) setSelectedSubOption(value);
      setValue?.(name as string, value);
    }

    if (onSelect) onSelect(value, key as string);
  };

  const getTextFieldValue = () => {
    if (selectedSubOption) return selectedSubOption;
    const selectedMainOption = options.find(
      (option) => option.value === selectedOption
    );
    if (selectedMainOption && !selectedMainOption.subOptions)
      return selectedOption;
    return '';
  };

  const errorClass = error ? 'border-red-500' : undefined;

  const handleClear = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedOption(null);
      setSelectedSubOption(null);
      setValue?.(name as string, '');

      if (onClear) {
        onClear();
      }
    },
    [setValue, name, onClear]
  );

  return (
    <>
      {disabledInput ? (
        <TextInput
          key={name}
          label={label}
          color="white"
          {...(register ? register(name as string) : {})}
          disabled={disabledInput}
          endDecoration={endDecoration}
          className={className}
        />
      ) : (
        <DropdownMenu>
          <DropdownMenuTrigger
            onClick={() => setIsOpen(true)}
            asChild
            className={`relative ${className}`}
          >
            <div className="w-full">
              <MultiLevelTextInput
                key={name}
                readOnly
                defaultValue={getTextFieldValue() ?? ''}
                fieldClassName={`border-green h-9 ${errorClass} ${fieldClass}`}
                selectValue={selectedValue ?? ''}
                {...(register && name ? register(name as string) : {})}
                label={label}
                placeholder={placeholder}
                endDecoration={
                  <div className="flex items-center ">
                    {isClearable && selectedValue && (
                      <IconButton
                        size="small"
                        onMouseEnter={() => setIsOpen(false)}
                        onMouseLeave={() => setIsOpen(true)}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleClear(e);
                        }}
                      >
                        <IoIosClose
                          size={24}
                          className="pointer-events-auto text-gray-500 hover:text-gray-700 cursor-pointer"
                        />
                      </IconButton>
                    )}
                    <IoIosArrowDown className="h-4 w-auto text-[#637D92]" />
                  </div>
                }
              />
            </div>
          </DropdownMenuTrigger>
          {!isMobile ? (
            <DropDown
              open={isOpen}
              options={options}
              handleSelect={handleSelect}
              isLoading={isLoading}
              maxHeight={maxHeight}
              isMultiLevel={isMultiLevel}
              loading={loading}
            />
          ) : (
            <MobileDropDown
              open={isOpen}
              options={options}
              handleSelect={handleSelect}
              isLoading={isLoading}
              maxHeight={maxHeight}
              isMultiLevel={isMultiLevel}
              loading={loading}
              onClose={() => setIsOpen(false)}
              value={selectedValue}
            />
          )}
        </DropdownMenu>
      )}
    </>
  );
}
