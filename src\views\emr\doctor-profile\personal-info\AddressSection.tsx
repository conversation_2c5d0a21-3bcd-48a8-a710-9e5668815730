import { useEffect, useState } from 'react';

import { Controller, FieldError, useFormContext } from 'react-hook-form';

import FileInput from '@core/components/file-input';
import TextInput from '@core/components/text-input';

import { cn } from '@/lib/utils';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePostalStore } from '@/store/emr/doctor-profile/personal-info/pincode';

import { states } from '@/utils/constants/master';
import {
  emailPattern,
  enforceNumericInput,
  mobileNumberPattern,
  residentialPhonePattern,
  restrictMaxLength,
  restrictMobile,
} from '@/utils/validation';

import SectionTitle from '@/views/emr/doctor-profile/personal-info/SectionTitle';

import { Checkbox } from '@/components/ui/checkbox';

import { AddressDetails } from '@/types/emr/doctor-profile/personal-info';

import { EditableSelectField, SelectField } from './Components';

export interface AddressSectionProps {
  className?: string;
  type: 'permanent' | 'current';
  isFieldDisabled: (_fieldName: string) => boolean;
  renderEditIcon: (_fieldName: string) => JSX.Element | null;
}

type ErrorsType = {
  address: Record<
    'permanent' | 'current',
    Partial<Record<'mobile' | keyof AddressDetails, FieldError>>
  >;
};

export default function AddressSection(props: AddressSectionProps) {
  const {
    register,
    control,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useFormContext();
  const {
    currentStreets,
    permanentStreets,
    setCurrentStreet,
    setPermanentStreet,
    locationDataByPincode,
    setPermanentPincode,
    setCurrentPincode,
    setHasCurrentPincode,
    setHasPermanentPincode,
  } = usePostalStore();

  const [isSameAsPermanent, setIsSameAsPermanent] = useState(false);
  const [originalCurrentAddress, setOriginalCurrentAddress] =
    useState<any>(null);
  const { doctorProfile } = useDoctorStore();

  const { type, isFieldDisabled, renderEditIcon } = props;

  const permanentAddress = watch('address.permanent');
  const permanentAddressProof = watch('address.permanent.proof');
  const permanentPincode = watch(`address.permanent.pinCode`);
  const permanentsSelectedStreets = watch(`address.permanent.street`);
  const currentPincode = watch(`address.current.pinCode`);
  const currentStreet = watch(`address.current.street`);

  useEffect(() => {
    if (permanentPincode?.length === 6) {
      setPermanentPincode(permanentPincode);
    } else {
      setPermanentPincode('');
      setPermanentStreet('');
      setValue(`address.permanent.street`, '');
      setValue(`address.permanent.city`, '');
      setValue(`address.permanent.district`, '');
      setValue(`address.permanent.state`, '');
      setValue(`address.permanent.country`, '');
      usePostalStore.getState().locationDataByPincode.permanent = null;
    }
  }, [permanentPincode, setPermanentPincode, setPermanentStreet, setValue]);

  useEffect(() => {
    if (currentPincode?.length === 6) {
      setCurrentPincode(currentPincode);
    } else {
      setCurrentPincode('');
      setCurrentStreet('');
      setValue(`address.current.street`, '');
      setValue(`address.current.city`, '');
      setValue(`address.current.district`, '');
      setValue(`address.current.state`, '');
      setValue(`address.current.country`, '');
      usePostalStore.getState().locationDataByPincode.current = null;
    }
  }, [currentPincode, setCurrentPincode, setCurrentStreet, setValue]);

  useEffect(() => {
    if (permanentsSelectedStreets) {
      setPermanentStreet(permanentsSelectedStreets);

      const selectedStreetObj = permanentStreets.find(
        (s) => s.value === permanentsSelectedStreets
      );

      if (selectedStreetObj?.city) {
        setValue(`address.permanent.city`, selectedStreetObj.city);
      }
    }
  }, [
    permanentsSelectedStreets,
    permanentStreets,
    setPermanentStreet,
    setValue,
  ]);

  useEffect(() => {
    if (currentStreet) {
      setCurrentStreet(currentStreet);

      const selectedStreetObj = currentStreets.find(
        (s) => s.value === currentStreet
      );

      if (selectedStreetObj?.city) {
        setValue(`address.current.city`, selectedStreetObj.city);
      }
    }
  }, [currentStreet, currentStreets, setCurrentStreet, setValue]);

  useEffect(() => {
    if (locationDataByPincode) {
      // Always update permanent address fields
      setValue(
        `address.permanent.district`,
        locationDataByPincode.permanent?.district
      );
      setValue(
        `address.permanent.state`,
        locationDataByPincode.permanent?.state
      );
      setValue(
        `address.permanent.country`,
        locationDataByPincode.permanent?.country
      );

      // Only update current address fields if not using "same as permanent"
      if (!isSameAsPermanent) {
        setValue(
          `address.current.district`,
          locationDataByPincode.current?.district
        );
        setValue(`address.current.state`, locationDataByPincode.current?.state);
        setValue(
          `address.current.country`,
          locationDataByPincode.current?.country
        );
      }
    }
  }, [locationDataByPincode, setValue, isSameAsPermanent]);

  // Initialize original current address data when component loads
  useEffect(() => {
    if (!originalCurrentAddress && doctorProfile?.personal?.address?.current) {
      setOriginalCurrentAddress(doctorProfile.personal.address.current);
    }
  }, [doctorProfile?.personal?.address?.current, originalCurrentAddress]);

  const handleCheckboxChange = (checked: boolean) => {
    if (checked) {
      // Store the current address data before overwriting it
      const currentAddressData = getValues('address.current');
      if (currentAddressData) {
        setOriginalCurrentAddress({ ...currentAddressData });
      }

      // Get all permanent address values
      const permanentAddressValues = getValues('address.permanent');

      // Copy all permanent address values to current address
      if (permanentAddressValues) {
        Object.keys(permanentAddressValues).forEach((key) => {
          if (key !== 'proof') {
            setValue(`address.current.${key}`, permanentAddressValues[key]);
          }
        });
      }

      // Handle proof separately
      if (permanentAddressProof) {
        setValue(`address.current.proof.url`, permanentAddressProof.url);
        setValue(
          `address.current.proof.description`,
          permanentAddressProof.description
        );
      }
    } else {
      // Restore the original current address data
      if (originalCurrentAddress) {
        Object.keys(originalCurrentAddress).forEach((key) => {
          if (
            key === 'proof' &&
            typeof originalCurrentAddress[key] === 'object'
          ) {
            // Handle proof object separately
            const proofData = originalCurrentAddress[key] as any;
            if (proofData) {
              setValue(`address.current.proof.url`, proofData.url || '');
              setValue(
                `address.current.proof.description`,
                proofData.description || ''
              );
            }
          } else {
            setValue(`address.current.${key}`, originalCurrentAddress[key]);
          }
        });
      } else {
        // Fallback to doctor profile data or clear fields
        const doctorCurrentAddress = doctorProfile?.personal?.address?.current;
        if (doctorCurrentAddress) {
          Object.keys(doctorCurrentAddress).forEach((key) => {
            setValue(
              `address.current.${key as keyof AddressDetails}`,
              doctorCurrentAddress[key as keyof AddressDetails]
            );
          });
        } else {
          // Clear all current address fields
          const currentFields = [
            'home',
            'pinCode',
            'street',
            'city',
            'district',
            'state',
            'country',
            'phone',
            'mobile',
            'email',
          ];
          currentFields.forEach((field) => {
            setValue(`address.current.${field}`, '');
          });
          setValue(`address.current.proof.url`, '');
          setValue(`address.current.proof.description`, '');
        }
      }
    }

    setIsSameAsPermanent(checked);
  };

  useEffect(() => {
    setHasPermanentPincode && setHasPermanentPincode(false);
    setHasCurrentPincode && setHasCurrentPincode(false);
  }, [setHasCurrentPincode, setHasPermanentPincode]);

  return (
    <div className={cn(props.className)}>
      <div className="flex justify-between items-center">
        <SectionTitle
          className="font-bold mb-4"
          title={type === 'current' ? 'Present' : 'Permanent'}
        />

        {type === 'current' && (
          <Checkbox
            label="Same as permanent"
            checked={isSameAsPermanent}
            onCheckedChange={handleCheckboxChange}
          />
        )}
      </div>

      <div className="flex flex-col w-full gap-4 md:gap-6">
        <TextInput
          key={`address.${type}.home`}
          label="Home"
          color="white"
          {...register(`address.${type}.home`)}
          disabled={isFieldDisabled(`address.${type}.home`)}
          endDecoration={renderEditIcon(`address.${type}.home`)}
          placeholder="House No/ Name"
        />
        <TextInput
          key="pinCode"
          label="Pin"
          color="white"
          {...register(`address.${type}.pinCode`)}
          disabled={isFieldDisabled(`address.${type}.pinCode`)}
          endDecoration={renderEditIcon(`address.${type}.pinCode`)}
          placeholder="685132"
          onKeyDown={restrictMaxLength(6)}
          onInput={enforceNumericInput({ maxLength: 6 })}
          pattern="[0-9]*"
          inputMode="numeric"
          onFocus={() => {
            if (type === 'current') {
              setHasCurrentPincode && setHasCurrentPincode(true);
            }
            if (type === 'permanent') {
              setHasPermanentPincode && setHasPermanentPincode(true);
            }
          }}
          onBlur={() => {
            if (type === 'current') {
              setHasCurrentPincode && setHasCurrentPincode(false);
            }
            if (type === 'permanent') {
              setHasPermanentPincode && setHasPermanentPincode(false);
            }
          }}
        />

        <EditableSelectField
          id={`address.${type}.street`}
          label="Street"
          options={
            type === 'current'
              ? currentStreets
              : type === 'permanent' ||
                  (type === 'current' && isSameAsPermanent)
                ? permanentStreets
                : []
          }
          value={watch(`address.${type}.street`)}
          wrapperClassName="flex-grow flex-1"
          onChange={(value) => setValue(`address.${type}.street`, value)}
          placeholder="Select"
          disabledInput={isFieldDisabled(`address.${type}.street`)}
          endDecoration={renderEditIcon(`address.${type}.street`)}
        />

        <TextInput
          key={`address.${type}.city`}
          label="City"
          color="white"
          {...register(`address.${type}.city`)}
          disabled={isFieldDisabled(`address.${type}.city`)}
          endDecoration={renderEditIcon(`address.${type}.city`)}
          placeholder="Chennai"
        />

        <TextInput
          key="district"
          label="District"
          color="white"
          {...register(`address.${type}.district`)}
          placeholder="Chennai"
          disabled={isFieldDisabled(`address.${type}.district`)}
          endDecoration={renderEditIcon(`address.${type}.district`)}
        />
        <SelectField
          id={`address.${type}.state`}
          label="State"
          options={states}
          value={watch(`address.${type}.state`)}
          wrapperClassName="flex-grow flex-1"
          onChange={(value) => setValue(`address.${type}.state`, value)}
          placeholder="Select"
          disabledInput={isFieldDisabled(`address.${type}.state`)}
          endDecoration={renderEditIcon(`address.${type}.state`)}
        />
        <TextInput
          key="country"
          label="Country"
          color="white"
          {...register(`address.${type}.country`)}
          disabled={isFieldDisabled(`address.${type}.country`)}
          endDecoration={renderEditIcon(`address.${type}.country`)}
          placeholder="India"
        />
        <TextInput
          key="phone"
          label="Residential Phone"
          color="white"
          {...register(`address.${type}.phone`, {
            pattern: residentialPhonePattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.phone`)}
          endDecoration={renderEditIcon(`address.${type}.phone`)}
          placeholder="1234567890"
          errors={(errors as ErrorsType).address?.[type]?.phone}
          onKeyDown={restrictMaxLength(11)}
          onInput={enforceNumericInput({ maxLength: 11 })}
          pattern="[0-9]*"
          inputMode="numeric"
        />
        <TextInput
          key="mobile"
          label={type === 'permanent' ? 'Personal Mobile' : 'Official Mobile'}
          color="white"
          {...register(`address.${type}.mobile`, {
            pattern: mobileNumberPattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.mobile`)}
          endDecoration={renderEditIcon(`address.${type}.mobile`)}
          placeholder="5520663388"
          onKeyDown={restrictMobile(10)}
          onInput={enforceNumericInput({ maxLength: 10 })}
          pattern="[0-9]*"
          inputMode="numeric"
          errors={(errors as ErrorsType).address?.[type]?.mobile}
        />
        <TextInput
          key="email"
          label={type === 'permanent' ? 'Personal Email' : 'Work Email'}
          color="white"
          {...register(`address.${type}.email`, {
            pattern: emailPattern(),
          })}
          disabled={isFieldDisabled(`address.${type}.email`)}
          endDecoration={renderEditIcon(`address.${type}.email`)}
          placeholder="<EMAIL>"
          errors={(errors as ErrorsType).address?.[type]?.email}
        />
        <Controller
          name={`address.${type}.proof.url`}
          control={control}
          render={({ field }) => (
            <FileInput
              label="Address Proof"
              value={field.value}
              onChange={(files) => field.onChange(files)}
              helperText={<AddressProofHelperText />}
              maxFileSize={6}
              allowedFileTypes={['image/jpeg', 'image/png', 'application/pdf']}
              fileTypeErrorMessage="*jpg/png/pdf files are allowed"
              showPreview
              isBoxStyle
            />
          )}
        />
        <TextInput
          key="proof.description"
          label="Proof Description"
          color="white"
          {...register(`address.${type}.proof.description`)}
          disabled={isFieldDisabled(`address.${type}.proof.description`)}
          endDecoration={renderEditIcon(`address.${type}.proof.description`)}
        />
      </div>
    </div>
  );
}

function AddressProofHelperText() {
  return (
    <div className="flex gap-2">
      <div>*jpg/png/pdf files are allowed</div>
      <div>*Maximum 6 MB</div>
    </div>
  );
}
