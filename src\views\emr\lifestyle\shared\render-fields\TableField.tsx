import React from 'react';

import {
  Controller,
  useFieldArray,
  useWatch,
  Control,
  useFormContext,
} from 'react-hook-form';

import { Button, IconButton, MenuItem, Select, TextField } from '@mui/material';
import { MdOutlineAdd, MdClose } from 'react-icons/md';

import DeleteModal from '@/core/components/delete-modal';
import { TableField as TableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

interface ConditionalSelectProps {
  controllerField: any;
  header: any;
  name: string;
  rowIndex: number;
  control: Control<any>;
  readonly?: boolean;
}

const ConditionalSelect: React.FC<ConditionalSelectProps> = ({
  controllerField,
  header,
  name,
  rowIndex,
  control,
  readonly,
}) => {
  const dependentValue = useWatch({
    control,
    name: header.dependsOn
      ? `${name}.value.${rowIndex}.${header.dependsOn}`
      : `${name}.value.${rowIndex}.nonexistent`,
  });

  const conditionalOptions = React.useMemo(() => {
    if (!header.dependsOn) return [];
    return typeof header.options === 'object' && !Array.isArray(header.options)
      ? header.options[dependentValue] || []
      : [];
  }, [header.dependsOn, header.options, dependentValue]);

  React.useEffect(() => {
    if (
      header.dependsOn &&
      dependentValue &&
      controllerField.value &&
      !conditionalOptions.includes(controllerField.value)
    ) {
      controllerField.onChange('');
    }
  }, [dependentValue, conditionalOptions, controllerField, header.dependsOn]);

  const actualDependentValue = header.dependsOn ? dependentValue : null;

  return (
    <Select
      {...controllerField}
      value={controllerField.value || ''}
      size="small"
      fullWidth
      variant="outlined"
      displayEmpty
      disabled={readonly || !actualDependentValue}
      sx={{
        minWidth: '150px',
        maxWidth: '200px',
        '& .MuiOutlinedInput-root': {
          backgroundColor: readonly ? '#f9fafb !important' : 'white !important',
          '&:hover': {
            backgroundColor: readonly
              ? '#f9fafb !important'
              : 'white !important',
          },
          '&.Mui-focused': {
            backgroundColor: readonly
              ? '#f9fafb !important'
              : 'white !important',
          },
        },
        '& .MuiSelect-select': {
          backgroundColor: readonly ? '#f9fafb !important' : 'white !important',
          '&:focus': {
            backgroundColor: readonly
              ? '#f9fafb !important'
              : 'white !important',
          },
        },
      }}
      renderValue={(selected) => {
        if (!selected) {
          return (
            <span style={{ color: '#9ca3af' }}>
              {actualDependentValue ? 'Select' : 'Select activity'}
            </span>
          );
        }
        return selected;
      }}
    >
      {conditionalOptions.map((option: string) => (
        <MenuItem key={option} value={option}>
          {option}
        </MenuItem>
      ))}
    </Select>
  );
};

export const TableField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
}) => {
  const tableField = field as TableFieldType;
  const [hasInitialized, setHasInitialized] = React.useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [rowToDelete, setRowToDelete] = React.useState<{
    index: number;
    rowData: any;
  } | null>(null);

  const { fields, append, remove } = useFieldArray({
    control,
    name: `${name}.value`,
  });

  const { getValues, watch } = useFormContext();

  const watchedValue = watch(`${name}.value`);

  const appendRef = React.useRef(append);
  appendRef.current = append;

  React.useEffect(() => {
    if (
      hasInitialized &&
      fields.length === 0 &&
      tableField.defaultRows &&
      tableField.defaultRows.length > 0
    ) {
      const currentFormValues = getValues();
      tableField.defaultRows.forEach((row, index) => {
        console.warn(`Re-adding default row ${index}:`, row);
        appendRef.current({ ...row });
      });
    }
  }, [
    fields.length,
    hasInitialized,
    tableField.defaultRows,
    name,
    getValues,
    watchedValue,
  ]);

  const addRow = () => {
    try {
      const newRow: Record<string, string> = {};
      tableField.headers.forEach((header) => {
        newRow[header.id] = '';
      });

      append(newRow);

      setTimeout(() => {
        const currentFormValues = getValues();
        const currentFieldValue = watch(`${name}.value`);
      }, 0);
      setTimeout(() => {
        const currentFieldValue = watch(`${name}.value`);
      }, 100);
    } catch (error) {
      console.error('Error during add row:', error);
    }
  };

  const handleDeleteClick = (index: number) => {
    const rowData = fields[index];
    setRowToDelete({ index, rowData });
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (rowToDelete !== null) {
      if (rowToDelete.index >= 0 && rowToDelete.index < fields.length) {
        try {
          remove(rowToDelete.index);
        } catch (error) {
          console.error('Error during deletion:', error);
        }
      } else {
        console.error(
          'Invalid row index for deletion:',
          rowToDelete.index,
          'fields.length:',
          fields.length
        );
      }

      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  };

  const getRowDisplayName = () => {
    if (!rowToDelete?.rowData) return 'this entry';

    const rowData = rowToDelete.rowData;
    if (rowData.activity) return rowData.activity;
    if (rowData.activity_type) return rowData.activity_type;
    if (rowData.name) return rowData.name;

    return 'this entry';
  };

  React.useEffect(() => {
    if (!hasInitialized) {
      if (
        fields.length === 0 &&
        tableField.defaultRows &&
        tableField.defaultRows.length > 0
      ) {
        const defaultRowsCopy = [...tableField.defaultRows];
        defaultRowsCopy.forEach((row) => {
          appendRef.current({ ...row }); // Create a copy to avoid reference issues
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  return (
    <div className="space-y-4">
      {!readonly && (
        <div className="flex justify-center">
          <Button
            variant="outlined"
            startIcon={<MdOutlineAdd />}
            onClick={addRow}
            size="small"
          >
            Add Entry
          </Button>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300">
          <thead
            style={{ backgroundColor: 'hsl(200 97% 25%)', color: '#ffffff' }}
          >
            <tr>
              {tableField.headers.map((header) => (
                <th
                  key={header.id}
                  className="px-4 py-2 text-left text-sm font-medium border-r border-gray-400 last:border-r-0"
                >
                  {header.label}
                </th>
              ))}
              {!readonly && (
                <th className="px-4 py-2 text-center text-sm font-medium w-16">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {fields.map((row, rowIndex) => (
              <tr key={row.id} className="border-b border-gray-200">
                {tableField.headers.map((header) => (
                  <td
                    key={header.id}
                    className="px-4 py-2 border-r border-gray-200 last:border-r-0"
                  >
                    <Controller
                      name={`${name}.value.${rowIndex}.${header.id}`}
                      control={control}
                      defaultValue=""
                      render={({ field: controllerField }) => {
                        if (
                          header.type === 'conditional_select' &&
                          header.options &&
                          header.dependsOn
                        ) {
                          return (
                            <ConditionalSelect
                              controllerField={controllerField}
                              header={header}
                              name={name}
                              rowIndex={rowIndex}
                              control={control}
                              readonly={readonly}
                            />
                          );
                        } else if (header.type === 'select' && header.options) {
                          const selectOptions = Array.isArray(header.options)
                            ? header.options
                            : [];
                          return (
                            <Select
                              {...controllerField}
                              value={controllerField.value || ''}
                              size="small"
                              fullWidth
                              variant="outlined"
                              displayEmpty
                              disabled={readonly}
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: readonly
                                    ? '#f9fafb !important'
                                    : 'white !important',
                                  '&:hover': {
                                    backgroundColor: readonly
                                      ? '#f9fafb !important'
                                      : 'white !important',
                                  },
                                  '&.Mui-focused': {
                                    backgroundColor: readonly
                                      ? '#f9fafb !important'
                                      : 'white !important',
                                  },
                                },
                                '& .MuiSelect-select': {
                                  backgroundColor: readonly
                                    ? '#f9fafb !important'
                                    : 'white !important',
                                  '&:focus': {
                                    backgroundColor: readonly
                                      ? '#f9fafb !important'
                                      : 'white !important',
                                  },
                                },
                              }}
                              renderValue={(selected) => {
                                if (!selected) {
                                  return (
                                    <span style={{ color: '#9ca3af' }}>
                                      Select
                                    </span>
                                  );
                                }
                                return selected;
                              }}
                            >
                              {selectOptions.map((option) => (
                                <MenuItem key={option} value={option}>
                                  {option}
                                </MenuItem>
                              ))}
                            </Select>
                          );
                        } else if (header.type === 'number') {
                          return (
                            <TextField
                              {...controllerField}
                              value={controllerField.value || ''}
                              type="number"
                              size="small"
                              fullWidth
                              variant="outlined"
                              disabled={readonly}
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: readonly
                                    ? '#f9fafb !important'
                                    : 'white !important',
                                },
                              }}
                            />
                          );
                        } else {
                          return (
                            <TextField
                              {...controllerField}
                              value={controllerField.value || ''}
                              size="small"
                              fullWidth
                              variant="outlined"
                              disabled={readonly}
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  backgroundColor: readonly
                                    ? '#f9fafb !important'
                                    : 'white !important',
                                },
                              }}
                            />
                          );
                        }
                      }}
                    />
                  </td>
                ))}
                {!readonly && (
                  <td className="px-4 py-2 text-center">
                    <IconButton
                      onClick={() => handleDeleteClick(rowIndex)}
                      size="small"
                      sx={{
                        color: '#ef4444',
                        '&:hover': {
                          backgroundColor: '#fef2f2',
                          color: '#dc2626',
                        },
                      }}
                    >
                      <MdClose size={18} />
                    </IconButton>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {fields.length === 0 && (
        <div className="text-center py-8 text-gray-500">No entries yet.</div>
      )}

      <DeleteModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onDelete={handleConfirmDelete}
        confirmationMessage={`Are you sure you want to delete ${getRowDisplayName()}?`}
      />
    </div>
  );
};
