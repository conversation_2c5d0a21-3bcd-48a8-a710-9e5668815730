import { Control, FieldError, FieldValues } from 'react-hook-form';

import { FormField } from '@/types/emr/lifestyle/questionnaire';

export interface FieldComponentProps<T extends FieldValues = any> {
  name: string;
  control: Control<T>;
  field: FormField;
  errors?: Record<string, FieldError>;
  watch?: (name: string) => any;
  setValue?: (name: string, value: any) => void;
  readonly?: boolean;
}
