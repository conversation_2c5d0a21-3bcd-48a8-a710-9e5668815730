import {
  forwardRef,
  memo,
  ReactNode,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useState,
} from 'react';

import { BlockNoteEditor, locales } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  blockTypeSelectItems,
  DragHandleButton,
  FormattingToolbar,
  FormattingToolbarController,
  SideMenu,
  SideMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { archivo } from '@/utils/fonts';

import './styles.scss';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

export type RichTextEditorProps = {
  id?: string;
  defaultValue?: string;
  bg?: 'white' | 'colored';
  maxHeight?: string;
  placeholder?: string;
  children?: ReactNode;
  onChange?: (text: string) => void;
  onImmediateChange?: (document: any) => void;
  onBlur?: () => void;
  className?: string;
};

export type RichTextEditorRef = {
  editor: BlockNoteEditor;
};

const RichTextEditor = forwardRef(
  (
    {
      id,
      defaultValue = '',
      bg = 'colored',
      maxHeight = '',
      placeholder = 'Enter diagnosis',
      children,
      onChange = () => {},
      onImmediateChange = () => {},
      onBlur = () => {},
      className,
    }: RichTextEditorProps,
    ref
  ) => {
    const defaultId = useId();
    const localId = id || defaultId;

    const editor = useCreateBlockNote(
      {
        initialContent: [
          {
            id: 'placeholder-block',
            type: 'paragraph',
            content: '',
          },
        ],
        dictionary: {
          ...locales.en,
          placeholders: {
            ...locales.en.placeholders,
            default: placeholder,
          },
        },
      },
      []
    );

    const allowedItems = [
      'paragraph',
      'bulletListItem',
      'numberedListItem',
      'checkListItem',
    ];
    const [blockTypeOptions] = useState(
      blockTypeSelectItems(editor.dictionary).filter((item) =>
        allowedItems.includes(item.type)
      )
    );

    const emitHTML = async () => {
      try {
        const htmlText = await editor.blocksToFullHTML(editor.document);
        onChange(htmlText);
      } catch {
        onChange('');
        toast.error('Error processing text input');
      }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedHandleChange = useCallback(debounce(emitHTML, 300), [
      editor,
    ]);

    const handleChange = () => {
      debouncedHandleChange();
      onImmediateChange(editor.document);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const loadHTMLToBlocks = useCallback(
      debounce(async () => {
        try {
          const blocks = await editor.tryParseHTMLToBlocks(defaultValue);
          editor.replaceBlocks(editor.document, blocks);
        } catch {
          toast.error('Parsing summary failed');
        }
      }, 300),
      [editor]
    );

    useImperativeHandle(ref, () => {
      return {
        editor,
      };
    }, [editor]);

    useEffect(() => {
      loadHTMLToBlocks();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [defaultValue]);

    return (
      <BlockNoteView
        id={localId}
        className={`
        rich-text-editor text-sm 
        ${bg} 
        ${className}
       
      `}
        style={{
          ...archivo.style,
          ...(maxHeight
            ? {
                maxHeight,
                overflow: 'auto',
              }
            : {}),
        }}
        editor={editor}
        sideMenu={false}
        slashMenu={false}
        formattingToolbar={false}
        onChange={handleChange}
        onBlur={onBlur}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
        <FormattingToolbarController
          formattingToolbar={() => (
            <FormattingToolbar blockTypeSelectItems={blockTypeOptions} />
          )}
        />

        {children}
      </BlockNoteView>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

const MemoRichTextEditor = memo(RichTextEditor);

export default MemoRichTextEditor;
