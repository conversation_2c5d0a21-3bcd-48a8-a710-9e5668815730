import { toast } from 'sonner';
import { create } from 'zustand';

import {
  createLifestyleData,
  getLifestyleQuestions,
  getPatientLifestyle,
  updateLifestyleData,
} from '@/query/emr/lifestyle';

import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import {
  FieldGroup,
  Questionnaire,
  QuestionnaireResponse,
} from '@/types/emr/lifestyle/questionnaire';

type AttitudeState = {
  questions: Questionnaire;
  questionLoading: boolean;
  updating: boolean;
  patientData: QuestionnaireResponse[];
  loading: boolean;
  finalizing: boolean;
};

type AttitudeActions = {
  getLifestyleQuestions: () => Promise<void>;
  createLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  updateLifestyleData: (data: Record<string, unknown>) => Promise<void>;
  finalizeRecord: (id: string) => Promise<void>;
  getPatientData: () => Promise<void>;
  refreshData: () => void;
};

export type AttitudeStore = AttitudeState & AttitudeActions;

const defaultQuestions = {
  source: LifestyleSources.PHYSICAL_ACTIVITY_ATTITUDE,
  questions: [],
};

const initialState: AttitudeState = {
  questions: defaultQuestions,
  questionLoading: false,
  updating: false,
  patientData: [],
  loading: false,
  finalizing: false,
};

export const attitudeStore = create<AttitudeStore>((set, get) => ({
  ...initialState,
  getLifestyleQuestions: async () => {
    try {
      set({ questionLoading: true });
      console.log('Fetching attitude questions...');

      const data = await getLifestyleQuestions(
        LifestyleSources.PHYSICAL_ACTIVITY_ATTITUDE
      );

      console.log('Received attitude questions data:', data);
      set({ questions: data });
    } catch (error) {
      console.error('Error fetching attitude questions:', error);
      toast.error('Failed to load attitude questionnaire. Please try again.');

      set({ questions: defaultQuestions });
    } finally {
      set({ questionLoading: false });
    }
  },

  createLifestyleData: async (data: Record<string, unknown>) => {
    try {
      set({ updating: true });
      const payload = {
        ...data,
        source: LifestyleSources.PHYSICAL_ACTIVITY_ATTITUDE,
      };
      await createLifestyleData(payload);
      toast.success('Attitude record created successfully');
      get().getPatientData();
    } catch (error) {
      console.error('Error creating attitude data:', error);
      toast.error('Failed to create attitude record');
    } finally {
      set({ updating: false });
    }
  },

  updateLifestyleData: async (data: Record<string, unknown>) => {
    try {
      set({ updating: true });

      const {
        questions,
        created_on,
        updated_on,
        create_by,
        update_by,
        _attachments,
        _etag,
        _rid,
        _self,
        _ts,
        ...formData
      } = data;

      const payload = {
        ...formData,
        source: LifestyleSources.PHYSICAL_ACTIVITY_ATTITUDE,
        questions: (questions || data.questions) as FieldGroup[],
      };

      await updateLifestyleData(payload, data.id as string);
      toast.success('Attitude record updated successfully');
      get().getPatientData();
    } catch (error) {
      console.error('Error updating attitude data:', error);
      toast.error('Failed to update attitude record');
    } finally {
      set({ updating: false });
    }
  },

  finalizeRecord: async (id: string) => {
    try {
      set({ finalizing: true });
      await updateLifestyleData(
        {
          id,
          status: LifestyleRecordStatus.FINALIZED,
        },
        id
      );
      toast.success('Attitude record finalized successfully');
      await get().getPatientData();
    } catch (error) {
      console.error('Error finalizing attitude record:', error);
      toast.error('Failed to finalize attitude record');
    } finally {
      set({ finalizing: false });
    }
  },

  getPatientData: async () => {
    try {
      set({ loading: true });
      const data = await getPatientLifestyle(
        LifestyleSources.PHYSICAL_ACTIVITY_ATTITUDE
      );
      set({ patientData: data || [] });
    } catch (error) {
      console.error('Error fetching patient data:', error);
      set({ patientData: [] });
    } finally {
      set({ loading: false });
    }
  },

  refreshData: () => {
    const { getPatientData } = get();
    getPatientData();
  },
}));
