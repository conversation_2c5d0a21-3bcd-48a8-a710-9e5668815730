import { useMemo, memo } from 'react';

import {
  Control,
  FieldArrayWithId,
  useFormState,
  useWatch,
} from 'react-hook-form';

import { TableCellProps } from '@mui/material';
import { TableContainerProps } from '@mui/material/TableContainer';

import {
  getActionButtonCellProps,
  frequencyOptions,
  prescriptionHeaders,
  routeOptions,
  getCellStyling,
  getInstructionsStyling,
} from '@/utils/constants/prescription';
import { allowOnlyNumbers, preventLeadingZero } from '@/utils/validation';
import { allowOnlyNumbersOnPaste } from '@/utils/validation';

import ActionButton from '@/views/emr/doctor-profile/personal-info/shared/ActionButton';
import TableTextarea from '@/views/emr/doctor-profile/personal-info/shared/TableTextarea';

import Table from '@/core/components/table';
import { Row } from '@/core/components/table/types';
import {
  defaultPrescriptionRow,
  PrescriptionItem,
} from '@/types/emr/prescription';

import { Prescription } from './NewPrescription';

import DurationSelector from './shared/DurationSelector';
import InstructionsSelector from './shared/InstructionsSelector';
import OptionsSelector from './shared/OptionSelector';
import { ControlledCheckbox } from './shared/TableCheckBox';

const tableContainerProps: TableContainerProps = {
  sx: {
    width: '100% !important',
    maxWidth: '100% !important',
    overflowX: 'auto',
    maxHeight: 'calc(100vh - 22rem)',
    '& tbody tr': { height: 30 },
    '& thead th': {
      borderLeft: '1px solid #64707D',
      backgroundColor: 'hsl(200 97% 25%) !important',
      color: '#ffffff',
      fontSize: '14px',
      fontWeight: 600,
      letterSpacing: '-0.02em',
      textAlign: 'center',
      padding: '8px',
    },
    '& tbody td': {
      padding: '4px 8px',
      fontSize: '14px',
      textAlign: 'center',
      border: '1px solid #E5E7EB',
    },
    '& tbody tr:last-child td': {
      borderBottom: 'none',
    },
    '& tbody tr:nth-of-type(odd)': {
      backgroundColor: 'hsla(200, 96%, 85%, 0.2) !important',
    },
    '& tbody tr:nth-of-type(even)': {
      backgroundColor: '#ffffff !important',
    },
    '& tbody tr:nth-of-type(odd) td': {
      backgroundColor: 'hsla(200, 96%, 85%, 0.1) !important',
    },
    '& tbody tr:nth-of-type(even) td': {
      backgroundColor: '#ffffff !important',
    },
    '& .MuiTable-root': {
      width: '100% !important',
      maxWidth: '100% !important',
      tableLayout: 'fixed',
      borderRadius: '8px',
      overflow: 'hidden',
      border: '1px solid #E5E7EB',
    },
    '& td': {
      wordWrap: 'break-word',
      whiteSpace: 'normal',
    },
  },
};

type Props = {
  fields: FieldArrayWithId<Prescription, 'prescription', 'id'>[];
  control: Control<Prescription, any>;
  isReadOnly: boolean;
  selectedMedicines: PrescriptionItem[];
  handleRemove: (index: number, id: string) => void;
  clearErrors: () => void;
  setValue: (name: `prescription.${number}.${string}`, value: any) => void;
};

const PrescriptionTable: React.FC<Props> = ({
  fields,
  control,
  isReadOnly,
  selectedMedicines,
  handleRemove,
  clearErrors,
  setValue,
}) => {
  const { errors } = useFormState({ control });
  const watchPrescription = useWatch({ control, name: 'prescription' });

  const filteredHeaders = useMemo(
    () =>
      prescriptionHeaders.filter(
        (header) => !(isReadOnly && header.key === 'close')
      ),
    [isReadOnly]
  );

  const rows = useMemo<Row[]>(
    () =>
      fields.map((_, index) => {
        const row: Row = {};
        const medicine = selectedMedicines[index];
        const medicineId = medicine?.id;
        const isNonEmpty = Object.keys(
          watchPrescription?.[index] ?? defaultPrescriptionRow
        ).some((key) => {
          if (key === 'canSubstitute')
            return watchPrescription?.[index]?.[key] === true;
          if (key === 'close' || key === 'id') return false;
          return watchPrescription?.[index]?.[key] !== '';
        });

        filteredHeaders.forEach(({ key }) => {
          const isValid = !errors?.prescription?.[index]?.[key];
          const hasError = !isValid && isNonEmpty;
          const cellStyling: TableCellProps = {
            ...getCellStyling({
              isReadOnly,
              isNonEmpty,
              isValid: !hasError,
            }),
            ...(hasError && {
              backgroundColor: '#FFF2F2',
              border: '1px solid #FF4D4F',
            }),
            sx: {
              ...getCellStyling({
                isReadOnly,
                isNonEmpty,
                isValid: !hasError,
              }).sx,
              backgroundColor: 'inherit !important',
            },
          };
          const instructionsStyling: TableCellProps = {
            ...getInstructionsStyling({
              isReadOnly,
              isNonEmpty,
              isValid: !hasError,
            }),
            ...(hasError && {
              backgroundColor: '#FFF2F2',
              border: '1px solid #FF4D4F',
            }),

            sx: {
              ...getInstructionsStyling({
                isReadOnly,
                isNonEmpty,
                isValid: !hasError,
              }).sx,
              backgroundColor: 'inherit !important',
            },
          };

          row[key] = generateCellContent(key, index, {
            isNonEmpty,
            isValid: !hasError,
            medicineId,
            cellStyling,
            instructionsStyling,
            control,
            clearErrors,
            setValue,
            isReadOnly,
            handleRemove,
            medicine,
          });
        });

        return row;
      }),
    [
      fields,
      selectedMedicines,
      watchPrescription,
      errors?.prescription,
      isReadOnly,
      control,
      clearErrors,
      setValue,
      handleRemove,
      filteredHeaders,
    ]
  );

  return (
    <Table
      headers={filteredHeaders}
      rows={rows}
      stickyHeader
      tableContainerProps={tableContainerProps}
      noDataMessage={
        <div className="flex h-full w-full justify-center items-center text-base font-medium">
          No prescription found
        </div>
      }
    />
  );
};

export default memo(PrescriptionTable);

const generateCellContent = (
  key: string,
  index: number,
  {
    isNonEmpty,
    isValid,
    medicineId,
    cellStyling,
    instructionsStyling,
    control,
    clearErrors,
    setValue,
    isReadOnly,
    handleRemove,
    medicine,
  }: {
    isNonEmpty: boolean;
    isValid: boolean;
    medicineId?: string;
    cellStyling: TableCellProps;
    instructionsStyling: TableCellProps;
    control: any;
    clearErrors: () => void;
    setValue: (name: `prescription.${number}.${string}`, value: any) => void;
    isReadOnly: boolean;
    handleRemove: (index: number, id: string) => void;
    medicine?: any;
  }
) => {
  if (!isNonEmpty) return { value: '', cellProps: cellStyling };

  switch (key) {
    case 'close':
      return {
        value: (
          <ActionButton
            actionFor="close"
            onClick={() => handleRemove(index, medicineId as string)}
            disabled={isReadOnly}
          />
        ),
        cellProps: getActionButtonCellProps(isReadOnly),
      };

    case 'frequency':
      return {
        value: (
          <OptionsSelector
            name={`prescription.${index}.${key}`}
            control={control}
            options={frequencyOptions}
            title="Frequency"
            isNotValid={isNonEmpty && !isValid}
            rules={{ required: { value: true } }}
          />
        ),
        cellProps: cellStyling,
      };

    case 'canSubstitute':
      return {
        value: (
          <ControlledCheckbox
            name={`prescription.${index}.${key}`}
            control={control}
          />
        ),
        cellProps: getActionButtonCellProps(isReadOnly),
      };

    case 'duration':
      return {
        value: (
          <DurationSelector
            name={`prescription.${index}.${key}`}
            control={control}
            clearErrors={clearErrors}
            isNotValid={isNonEmpty && !isValid}
            rules={{ required: { value: true } }}
            setValue={setValue}
          />
        ),
        cellProps: cellStyling,
      };

    case 'route':
      return {
        value: (
          <OptionsSelector
            name={`prescription.${index}.${key}`}
            control={control}
            options={routeOptions}
            title="Route"
            isNotValid={isNonEmpty && !isValid}
            rules={{ required: { value: true } }}
          />
        ),
        cellProps: cellStyling,
      };

    case 'instructions':
      return {
        value: (
          <InstructionsSelector
            name={`prescription.${index}.${key}`}
            control={control}
            clearErrors={clearErrors}
            isNotValid={isNonEmpty && !isValid}
            rules={{ required: { value: true } }}
            setValue={setValue}
          />
        ),
        cellProps: instructionsStyling,
      };

    case 'cost':
      return {
        value: (
          <TableTextarea
            name={`prescription.${index}.${key}`}
            control={control}
            rules={{ required: 'This field is required' }}
            showInputError={false}
            disabled={false}
            readOnly={true}
          />
        ),
        cellProps: cellStyling,
      };
    case 'quantity':
      const handleQuantityKeyDown: React.KeyboardEventHandler<
        HTMLTextAreaElement
      > = (e) => {
        allowOnlyNumbers(e);
        preventLeadingZero(e.currentTarget.value, e.key, e);
      };

      const handleQuantityPaste: React.ClipboardEventHandler<
        HTMLTextAreaElement
      > = (e) => {
        allowOnlyNumbersOnPaste(e);
        const pasted = e.clipboardData.getData('Text').trim();
        const isLeadingZero = pasted.length && pasted.startsWith('0');
        if (isLeadingZero) {
          e.preventDefault();
        }
      };
      return {
        value: (
          <TableTextarea
            name={`prescription.${index}.${key}`}
            control={control}
            rules={{
              required: true,
              validate: (val) => Number(val) > 0,
            }}
            showInputError={false}
            onChange={(
              e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
            ) => {
              const quantityValue = e.target.value;
              if (!quantityValue) {
                return;
              } else {
                const quantity = parseFloat(quantityValue);
                const costPerUnit = parseFloat(String(medicine?.Cost || '0'));
                if (!isNaN(quantity) && !isNaN(costPerUnit)) {
                  const totalCost = (quantity * costPerUnit).toFixed(2);
                  setValue(`prescription.${index}.cost`, totalCost);
                }
              }
            }}
            onKeyDown={handleQuantityKeyDown}
            onPaste={handleQuantityPaste}
          />
        ),
        cellProps: {
          ...cellStyling,
          sx: { ...cellStyling.sx, minWidth: 10 },
        },
      };
    case 'cost':
      return {
        value: (
          <TableTextarea
            name={`prescription.${index}.${key}`}
            control={control}
            rules={{ required: true }}
            showInputError={false}
            readOnly={true}
            defaultValue={medicine?.Cost}
          />
        ),
        cellProps: cellStyling,
      };

    case 'uom':
      return {
        value: (
          <TableTextarea
            name={`prescription.${index}.${key}`}
            control={control}
            showInputError={false}
            readOnly={true}
            defaultValue={medicine?.UnitOfMeasure}
          />
        ),
        cellProps: {
          ...cellStyling,
          sx: { ...cellStyling.sx, minWidth: 10 },
        },
      };

    default:
      const medicineFieldMap: Record<string, string> = {
        drugForm: 'DrugFormulation',
        genericName: 'GenericName',
        brandName: 'BrandName',
        strength: 'Strength',
        measure: 'Measure',
      };

      const medicineValue = medicineFieldMap[key]
        ? medicine?.[medicineFieldMap[key]]
        : '';

      return {
        value: (
          <TableTextarea
            name={`prescription.${index}.${key}`}
            control={control}
            showInputError={false}
            readOnly={true}
            defaultValue={medicineValue}
          />
        ),
        cellProps: cellStyling,
      };
  }
};
