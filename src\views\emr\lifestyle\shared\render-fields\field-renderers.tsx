import React, { useCallback } from 'react';

import { Controller } from 'react-hook-form';

import { TextareaAutosize } from '@mui/material';

import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';

import AppRadio from '@/core/components/app-radio';
import AppSlider from '@/core/components/app-slider';
import AppTextField from '@/core/components/app-text-field';
import {
  RadioField as IRadioField,
  SliderField as ISliderField,
  ConditionalField as IConditionalField,
  NumberField as INumberField,
  TextField as ITextField,
  SectionField as ISectionField,
} from '@/types/emr/lifestyle/questionnaire';

import { GroupedTableField } from './GroupedTableField';
import { TableField } from './TableField';
import { FieldComponentProps } from './types';

export const RadioField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  fieldIndex = 0,
}) => {
  const radioField = field as IRadioField;
  const radioOptions = radioField.options.map((option: string) => ({
    value: option,
    label: option,
  }));

  const isKnowledgeQuestion =
    field.label?.toLowerCase().includes('benefit of exercise') ||
    field.label?.toLowerCase().includes('examples of') ||
    field.label?.toLowerCase().includes('training') ||
    field.label?.toLowerCase().includes('exercise') ||
    field.label?.toLowerCase().includes('walking') ||
    field.label?.toLowerCase().includes('material') ||
    field.label?.toLowerCase().includes('food') ||
    field.label?.toLowerCase().includes('minimum') ||
    field.label?.toLowerCase().includes('which') ||
    field.label?.toLowerCase().includes('what is');

  if (readonly) {
    if (isKnowledgeQuestion) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            const isEvenRow = fieldIndex % 2 === 0;
            const rowBgColor = isEvenRow ? 'bg-sky-100' : 'bg-white';

            return (
              <div
                className={`grid grid-cols-5 gap-0 border-b border-gray-100 min-h-[60px] items-start ${rowBgColor}`}
              >
                <div className="p-2 flex items-start">
                  <span className="text-sm text-gray-700 leading-relaxed break-words">
                    {field.label}
                  </span>
                </div>

                {[0, 1, 2, 3].map((optionIndex) => {
                  const option = radioOptions[optionIndex];
                  const isChecked = fieldValue === option?.value;

                  return (
                    <div
                      key={optionIndex}
                      className="relative flex justify-center"
                      style={{ paddingTop: '12px' }}
                    >
                      {option ? (
                        <label className="flex items-start cursor-default max-w-full px-2">
                          <input
                            type="radio"
                            name={name}
                            value={option.value}
                            checked={isChecked}
                            className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 flex-shrink-0"
                            disabled={readonly}
                            readOnly={readonly}
                            style={{ marginTop: '0px' }}
                          />
                          <span className="ml-2 text-xs text-gray-600 leading-tight break-words">
                            {option.label}
                          </span>
                        </label>
                      ) : (
                        <div className="w-4 h-4"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          }}
        />
      );
    }

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { value } }) => {
          const fieldValue =
            (field as any).value !== undefined ? (field as any).value : value;

          return (
            <div className="flex flex-col">
              <span className="text-base font-medium">Q. {field.label}</span>
              <span className="text-base">
                A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                {fieldValue || 'No answer provided'}
              </span>
            </div>
          );
        }}
      />
    );
  }

  const isMainExerciseQuestion = field.label
    ?.toLowerCase()
    .includes('do you like to exercise');

  const defaultFormControlProps = {
    sx: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      gap: isMainExerciseQuestion ? 1 : isKnowledgeQuestion ? 0.5 : 2,
      alignItems: 'center',
      minHeight: isKnowledgeQuestion ? '32px' : 'auto',
      ['& .app-input-label']: {
        minWidth: isKnowledgeQuestion ? 250 : 400,
        maxWidth: isKnowledgeQuestion ? 250 : 'none',
        fontSize: isKnowledgeQuestion ? '13px' : '16px',
        lineHeight: isKnowledgeQuestion ? '1.2' : 'normal',
        ...(isMainExerciseQuestion && {
          fontWeight: 'bold',
          marginBottom: '4px',
        }),
        ...(isKnowledgeQuestion && {
          fontWeight: '400',
          marginBottom: '0px',
          paddingRight: '8px',
        }),
      },
      ['& .MuiRadioGroup-root']: {
        ...(isKnowledgeQuestion && {
          gap: '4px',
          flexWrap: 'wrap',
        }),
      },
      ['& .MuiFormControlLabel-root']: {
        ...(isKnowledgeQuestion && {
          margin: '0 8px 0 0',
          '& .MuiFormControlLabel-label': {
            fontSize: '12px',
            lineHeight: '1.2',
          },
        }),
      },
    },
  };

  const formControlProps =
    (radioField as any).formControlProps || defaultFormControlProps;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          (field as any).value !== undefined ? (field as any).value : value;

        const radioComponent = (
          <AppRadio
            name={name}
            value={fieldValue}
            onChange={onChange}
            label={field.label}
            options={radioOptions}
            errors={errors?.[name]}
            required={isMainExerciseQuestion ? false : field.required}
            disabled={readonly}
            formControlProps={formControlProps}
          />
        );

        if (isMainExerciseQuestion) {
          return (
            <div className=" border-b border-gray-300 py-4">
              {radioComponent}
            </div>
          );
        } else if (isKnowledgeQuestion) {
          const isEvenRow = fieldIndex % 2 === 0;
          const rowBgColor = isEvenRow ? 'bg-sky-100' : 'bg-white';

          return (
            <div
              className={`grid grid-cols-5 gap-0 border-b border-gray-100 hover:bg-gray-50 min-h-[60px] items-start ${rowBgColor}`}
            >
              <div className="p-2 flex items-start">
                <span className="text-sm text-gray-700 leading-relaxed break-words">
                  {field.label}
                </span>
              </div>

              {[0, 1, 2, 3].map((optionIndex) => {
                const option = radioOptions[optionIndex];
                return (
                  <div
                    key={optionIndex}
                    className="relative flex justify-center"
                    style={{ paddingTop: '12px' }}
                  >
                    {option ? (
                      <label className="flex items-start cursor-pointer max-w-full px-2">
                        <input
                          type="radio"
                          name={name}
                          value={option.value}
                          checked={fieldValue === option.value}
                          onChange={() => onChange(option.value)}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 flex-shrink-0"
                          disabled={readonly}
                          style={{ marginTop: '0px' }}
                        />
                        <span className="ml-2 text-xs text-gray-600 leading-tight break-words">
                          {option.label}
                        </span>
                      </label>
                    ) : (
                      <div className="w-4 h-4"></div>
                    )}
                  </div>
                );
              })}
            </div>
          );
        } else {
          return radioComponent;
        }
      }}
    />
  );
};

export const SliderField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  const sliderField = field as ISliderField;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        return (
          <AppSlider
            value={fieldValue ?? sliderField.min}
            onChange={onChange}
            label={field.label}
            min={sliderField.min}
            max={sliderField.max}
            step={sliderField.step}
            helperText={field.description}
            errors={errors?.[name]}
            required={field.required}
            showIndicator
            disabled={readonly}
          />
        );
      }}
    />
  );
};

export const TextField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  const numberField = field as INumberField | ITextField;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: formField }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : formField.value;

        return (
          <AppTextField
            {...formField}
            value={fieldValue}
            type={numberField.type}
            label={numberField.label}
            placeholder={numberField.label}
            required={numberField.required}
            error={!!errors?.[name]}
            disabled={readonly}
            initiallyReadonly
          />
        );
      }}
    />
  );
};

export const ConditionalField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  watch,
  readonly,
}) => {
  const conditionalField = field as IConditionalField;
  const currentValue = watch?.(name);
  const selectedCondition = conditionalField.conditions.find(
    (cond) => cond.label === currentValue?.value
  );

  const renderAdditionField = useCallback(
    (optionLabel: string) => {
      if (!selectedCondition?.subField) return null;
      const { label, ...subField } = selectedCondition.subField;
      const inputLabel = label;

      if (optionLabel === selectedCondition.label) {
        return (
          <div className="flex gap-base items-center">
            <span className="whitespace-nowrap font-semibold">
              {inputLabel}
            </span>
            <TextField
              name={`${name}.subField`}
              field={subField}
              control={control}
              readonly={readonly}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    [selectedCondition, control, name, readonly]
  );

  if (readonly) {
    if (!currentValue) return null;
    if (currentValue?.value === 'Yes') {
      return (
        <span className="text-base">
          I drink {currentValue?.subField} cups of {field.label}
        </span>
      );
    } else if (currentValue?.value === 'No') {
      return (
        <span className="text-base">No, I don&apos;t drink {field.label}</span>
      );
    }
    return null;
  }

  return (
    <div className="space-y-4">
      <Controller
        name={`${name}.value`}
        control={control}
        render={({ field: { onChange, value } }) => (
          <AppRadio
            name={`${name}.value`}
            value={value}
            onChange={onChange}
            label={field.label}
            options={conditionalField.conditions.map((cond) => ({
              value: cond.label ?? '',
              label: (
                <div className="flex gap-10 items-center">
                  {cond.label}
                  {renderAdditionField(cond?.label ?? '')}
                </div>
              ),
            }))}
            disabled={readonly}
            errors={errors?.[name]}
            required={field.required}
            formControlProps={{
              sx: {
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                ['& .app-input-label']: { minWidth: 200 },
              },
            }}
          />
        )}
      />
    </div>
  );
};

export const TextareaField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors: _errors,
  readonly,
}) => {
  const isPhysicalChangeField = field.label
    ?.toLowerCase()
    .includes('physical change');
  const minRows = isPhysicalChangeField ? 1 : 3;

  const placeholder = isPhysicalChangeField ? '' : field.label;

  return (
    <div className="space-y-2">
      {isPhysicalChangeField && (
        <label className="block text-sm font-medium text-gray-700">
          {field.label}
        </label>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field: controllerField }) => {
          const fieldValue =
            readonly && (field as any).value !== undefined
              ? (field as any).value
              : controllerField.value;

          return (
            <TextareaAutosize
              {...controllerField}
              value={fieldValue}
              placeholder={placeholder}
              minRows={minRows}
              disabled={readonly}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontFamily: 'inherit',
                fontSize: '14px',
                resize: 'vertical',
              }}
            />
          );
        }}
      />
    </div>
  );
};

export const SectionField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  watch,
  setValue,
  readonly,
}) => {
  const sectionField = field as ISectionField;

  const isReasonSection = field.label
    ?.toLowerCase()
    .includes('reason for your choice');

  const isKnowledgeSection =
    field.label?.toLowerCase().includes('knowledge') ||
    field.label?.toLowerCase().includes('benefit of exercise') ||
    field.label?.toLowerCase().includes('examples of') ||
    field.label?.toLowerCase().includes('training') ||
    field.label?.toLowerCase().includes('exercise') ||
    field.label?.toLowerCase().includes('walking') ||
    field.label?.toLowerCase().includes('material') ||
    field.label?.toLowerCase().includes('food') ||
    field.label?.toLowerCase().includes('minimum') ||
    field.label?.toLowerCase().includes('which') ||
    field.label?.toLowerCase().includes('what is');

  if ((isReasonSection || isKnowledgeSection) && sectionField.fields) {
    return (
      <div className={isKnowledgeSection ? 'space-y-1' : 'space-y-4'}>
        <h4
          className={`font-medium ${isKnowledgeSection ? 'text-sm mb-2' : 'text-base'}`}
        >
          {field.label}
        </h4>
        <div
          className={`${isKnowledgeSection ? 'bg-white border border-gray-200 rounded-lg overflow-hidden' : 'rounded-lg overflow-hidden border border-gray-200'}`}
        >
          {sectionField.fields.map((subField, index) => {
            const fieldName = `${name}.fields.${index}.value`;
            const radioField = subField as IRadioField;

            const whiteBackgroundLabels = [
              'Availability of Gyms',
              'Subjective Feeling',
              'Social Support',
            ];
            const isWhiteBackground = whiteBackgroundLabels.includes(
              subField.label || ''
            );
            const rowBgColor = isWhiteBackground ? 'bg-white' : 'bg-sky-100';

            return (
              <div
                key={subField.id}
                className={`${isKnowledgeSection ? 'grid grid-cols-5 gap-0 border-b border-gray-100 last:border-b-0 hover:bg-gray-50' : `grid grid-cols-3 border-b border-gray-200 last:border-b-0 ${rowBgColor}`}`}
                style={
                  isKnowledgeSection
                    ? { display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)' }
                    : undefined
                }
              >
                {isKnowledgeSection ? (
                  <>
                    <div className="p-2 flex items-start">
                      <span className="text-sm text-gray-700 leading-relaxed break-words">
                        {subField.label}
                      </span>
                    </div>

                    {[0, 1, 2, 3].map((optionIndex) => {
                      const option = radioField.options?.[optionIndex];
                      return (
                        <div
                          key={optionIndex}
                          className="p-2 flex items-center justify-center"
                        >
                          {option && (
                            <Controller
                              name={fieldName}
                              control={control}
                              render={({ field: { onChange, value } }) => {
                                const fieldValue =
                                  readonly &&
                                  (subField as any).value !== undefined
                                    ? (subField as any).value
                                    : value;

                                return (
                                  <label className="flex items-center space-x-1.5 cursor-pointer">
                                    <input
                                      type="radio"
                                      value={option}
                                      checked={fieldValue === option}
                                      onChange={() => onChange(option)}
                                      disabled={readonly}
                                      className={`w-3.5 h-3.5 border-gray-300 focus:ring-blue-500 ${readonly ? 'text-blue-800 accent-blue-800' : 'text-blue-600'}`}
                                    />
                                    <span className="text-sm text-gray-700">
                                      {option}
                                    </span>
                                  </label>
                                );
                              }}
                            />
                          )}
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <>
                    <div
                      className={`p-3 flex items-center border-r border-black ${rowBgColor}`}
                    >
                      <span className="font-medium text-sm">
                        {subField.label}
                      </span>
                    </div>

                    <div className="col-span-2 grid grid-cols-2">
                      {radioField.options?.map((option, optionIndex) => (
                        <div
                          key={option}
                          className={`p-3 flex items-center ${optionIndex < (radioField.options?.length || 0) - 1 ? 'border-r border-black' : ''}`}
                        >
                          <Controller
                            name={fieldName}
                            control={control}
                            render={({ field: { onChange, value } }) => {
                              const fieldValue =
                                readonly &&
                                (subField as any).value !== undefined
                                  ? (subField as any).value
                                  : value;

                              return (
                                <label className="flex items-center space-x-2 cursor-pointer">
                                  <input
                                    type="radio"
                                    value={option}
                                    checked={fieldValue === option}
                                    onChange={() => onChange(option)}
                                    disabled={readonly}
                                    className={`w-4 h-4 border-gray-300 focus:ring-blue-500 ${readonly ? 'text-blue-800 accent-blue-800' : 'text-blue-600'}`}
                                  />
                                  <span className="text-sm">{option}</span>
                                </label>
                              );
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  const isPhysicalChangesSection = field.label
    ?.toLowerCase()
    .includes('rank the top 3 physical changes');

  if (isPhysicalChangesSection && sectionField.fields) {
    return (
      <div className="space-y-4">
        <h4 className="text-base font-medium">{field.label}</h4>
        <div className="space-y-6">
          {sectionField.fields &&
            Array.from(
              { length: Math.ceil(sectionField.fields.length / 3) },
              (_, groupIndex) => {
                const startIndex = groupIndex * 3;
                const textareaField = sectionField.fields![startIndex];
                const importanceField = sectionField.fields![startIndex + 1];
                const confidenceField = sectionField.fields![startIndex + 2];

                if (!textareaField || !importanceField || !confidenceField)
                  return null;

                const TextareaComponent = fieldComponents[textareaField.type];
                const ImportanceComponent =
                  fieldComponents[importanceField.type];
                const ConfidenceComponent =
                  fieldComponents[confidenceField.type];

                if (
                  !TextareaComponent ||
                  !ImportanceComponent ||
                  !ConfidenceComponent
                ) {
                  return null;
                }

                return (
                  <div key={`group-${groupIndex}`} className="space-y-3">
                    <div>
                      <TextareaComponent
                        name={`${name}.fields.${startIndex}.value`}
                        field={textareaField}
                        control={control}
                        watch={watch}
                        setValue={setValue}
                        readonly={readonly}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-sm font-medium text-gray-700">
                        {importanceField.label}
                      </div>
                      <div className="text-sm font-medium text-gray-700">
                        {confidenceField.label}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <ImportanceComponent
                          name={`${name}.fields.${startIndex + 1}.value`}
                          field={{ ...importanceField, label: '' }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                        />
                      </div>
                      <div>
                        <ConfidenceComponent
                          name={`${name}.fields.${startIndex + 2}.value`}
                          field={{ ...confidenceField, label: '' }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                        />
                      </div>
                    </div>
                  </div>
                );
              }
            )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-base font-medium">{field.label}</h4>
      {sectionField.fields && sectionField.fields.length > 0 && (
        <div className="space-y-4">
          {sectionField.fields.map((subField, index) => {
            const FieldComponent = fieldComponents[subField.type];
            if (!FieldComponent) {
              console.warn(
                `No renderer found for field type: ${subField.type}`
              );
              return null;
            }

            const fieldName =
              subField.type === 'table'
                ? `${name}.fields.${index}`
                : `${name}.fields.${index}.value`;

            return (
              <div key={subField.id} className="ml-4">
                <FieldComponent
                  name={fieldName}
                  field={subField}
                  control={control}
                  watch={watch}
                  setValue={setValue}
                  readonly={readonly}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const fieldComponents: Record<
  LifestyleFields,
  React.FC<FieldComponentProps>
> = {
  radio: RadioField,
  slider: SliderField,
  number: TextField,
  conditional: ConditionalField,
  text: TextField,
  table: TableField,
  textarea: TextareaField,
  section: SectionField,
  grouped_table: GroupedTableField,
  time_range: TextField,
} as const;
