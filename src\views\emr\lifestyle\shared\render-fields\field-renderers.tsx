import React, { useCallback } from 'react';

import { Controller } from 'react-hook-form';

import { TextareaAutosize } from '@mui/material';

import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';

import AppRadio from '@/core/components/app-radio';
import AppSlider from '@/core/components/app-slider';
import AppTextField from '@/core/components/app-text-field';
import {
  RadioField as IRadioField,
  SliderField as ISliderField,
  ConditionalField as IConditionalField,
  NumberField as INumberField,
  TextField as ITextField,
  SectionField as ISectionField,
} from '@/types/emr/lifestyle/questionnaire';

import { GroupedTableField } from './GroupedTableField';
import { TableField } from './TableField';
import { FieldComponentProps } from './types';

export const RadioField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  const radioField = field as IRadioField;
  const radioOptions = radioField.options.map((option: string) => ({
    value: option,
    label: option,
  }));

  if (readonly) {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { value } }) => (
          <div className="flex flex-col">
            <span className="text-base font-medium">Q. {field.label}</span>
            <span className="text-base">
              A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{value || 'No answer provided'}
            </span>
          </div>
        )}
      />
    );
  }

  const isMainExerciseQuestion = field.label
    ?.toLowerCase()
    .includes('do you like to exercise');

  const defaultFormControlProps = {
    sx: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      gap: isMainExerciseQuestion ? 1 : 2,
      ['& .app-input-label']: {
        minWidth: 400,
        ...(isMainExerciseQuestion && {
          fontWeight: 'bold',
          marginBottom: '4px',
        }),
      },
    },
  };

  const formControlProps =
    (radioField as any).formControlProps || defaultFormControlProps;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const radioComponent = (
          <AppRadio
            name={name}
            value={value}
            onChange={onChange}
            label={field.label}
            options={radioOptions}
            errors={errors?.[name]}
            required={isMainExerciseQuestion ? false : field.required}
            disabled={readonly}
            formControlProps={formControlProps}
          />
        );

        return isMainExerciseQuestion ? (
          <div className=" border-b border-gray-300 py-4">{radioComponent}</div>
        ) : (
          radioComponent
        );
      }}
    />
  );
};

export const SliderField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  const sliderField = field as ISliderField;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => (
        <AppSlider
          value={value ?? sliderField.min}
          onChange={onChange}
          label={field.label}
          min={sliderField.min}
          max={sliderField.max}
          step={sliderField.step}
          helperText={field.description}
          errors={errors?.[name]}
          required={field.required}
          showIndicator
          disabled={readonly}
        />
      )}
    />
  );
};

export const TextField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  const numberField = field as INumberField | ITextField;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <AppTextField
          {...field}
          type={numberField.type}
          label={numberField.label}
          placeholder={numberField.label}
          required={numberField.required}
          error={!!errors?.[name]}
          disabled={readonly}
          initiallyReadonly
        />
      )}
    />
  );
};

export const ConditionalField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  watch,
  readonly,
}) => {
  const conditionalField = field as IConditionalField;
  const currentValue = watch?.(name);
  const selectedCondition = conditionalField.conditions.find(
    (cond) => cond.label === currentValue?.value
  );

  const renderAdditionField = useCallback(
    (optionLabel: string) => {
      if (!selectedCondition?.subField) return null;
      const { label, ...subField } = selectedCondition.subField;
      const inputLabel = label;

      if (optionLabel === selectedCondition.label) {
        return (
          <div className="flex gap-base items-center">
            <span className="whitespace-nowrap font-semibold">
              {inputLabel}
            </span>
            <TextField
              name={`${name}.subField`}
              field={subField}
              control={control}
              readonly={readonly}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    [selectedCondition, control, name, readonly]
  );

  if (readonly) {
    if (!currentValue) return null;
    if (currentValue?.value === 'Yes') {
      return (
        <span className="text-base">
          I drink {currentValue?.subField} cups of {field.label}
        </span>
      );
    } else if (currentValue?.value === 'No') {
      return (
        <span className="text-base">No, I don&apos;t drink {field.label}</span>
      );
    }
    return null;
  }

  return (
    <div className="space-y-4">
      <Controller
        name={`${name}.value`}
        control={control}
        render={({ field: { onChange, value } }) => (
          <AppRadio
            name={`${name}.value`}
            value={value}
            onChange={onChange}
            label={field.label}
            options={conditionalField.conditions.map((cond) => ({
              value: cond.label ?? '',
              label: (
                <div className="flex gap-10 items-center">
                  {cond.label}
                  {renderAdditionField(cond?.label ?? '')}
                </div>
              ),
            }))}
            disabled={readonly}
            errors={errors?.[name]}
            required={field.required}
            formControlProps={{
              sx: {
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                ['& .app-input-label']: { minWidth: 200 },
              },
            }}
          />
        )}
      />
    </div>
  );
};

export const TextareaField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors: _errors,
  readonly,
}) => {
  const isPhysicalChangeField = field.label
    ?.toLowerCase()
    .includes('physical change');
  const minRows = isPhysicalChangeField ? 1 : 3;

  const placeholder = isPhysicalChangeField ? '' : field.label;

  return (
    <div className="space-y-2">
      {isPhysicalChangeField && (
        <label className="block text-sm font-medium text-gray-700">
          {field.label}
        </label>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field: controllerField }) => (
          <TextareaAutosize
            {...controllerField}
            placeholder={placeholder}
            minRows={minRows}
            disabled={readonly}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontFamily: 'inherit',
              fontSize: '14px',
              resize: 'vertical',
            }}
          />
        )}
      />
    </div>
  );
};

export const SectionField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  watch,
  setValue,
  readonly,
}) => {
  const sectionField = field as ISectionField;

  const isReasonSection = field.label
    ?.toLowerCase()
    .includes('reason for your choice');

  if (isReasonSection && sectionField.fields) {
    return (
      <div className="space-y-4">
        <h4 className="text-base font-medium">{field.label}</h4>
        <div className="rounded-lg overflow-hidden border border-gray-200">
          {sectionField.fields.map((subField, index) => {
            const fieldName = `${name}.fields.${index}.value`;
            const radioField = subField as IRadioField;

            const whiteBackgroundLabels = [
              'Availability of Gyms',
              'Subjective Feeling',
              'Social Support',
            ];
            const isWhiteBackground = whiteBackgroundLabels.includes(
              subField.label || ''
            );
            const rowBgColor = isWhiteBackground ? 'bg-white' : 'bg-sky-100';

            return (
              <div
                key={subField.id}
                className={`grid grid-cols-3 border-b border-gray-200 last:border-b-0 ${rowBgColor}`}
              >
                <div
                  className={`p-3 flex items-center border-r border-black ${rowBgColor}`}
                >
                  <span className="text-sm font-medium">{subField.label}</span>
                </div>

                <div className="col-span-2 grid grid-cols-2">
                  {radioField.options?.map((option, optionIndex) => (
                    <div
                      key={option}
                      className={`p-3 flex items-center ${optionIndex % 2 === 0 ? 'border-r border-black' : ''}`}
                    >
                      <Controller
                        name={fieldName}
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              value={option}
                              checked={value === option}
                              onChange={() => onChange(option)}
                              disabled={readonly}
                              className={`w-4 h-4 border-gray-300 focus:ring-blue-500 ${readonly ? 'text-blue-800 accent-blue-800' : 'text-blue-600'}`}
                            />
                            <span className="text-sm">{option}</span>
                          </label>
                        )}
                      />
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  const isPhysicalChangesSection = field.label
    ?.toLowerCase()
    .includes('rank the top 3 physical changes');

  if (isPhysicalChangesSection && sectionField.fields) {
    return (
      <div className="space-y-4">
        <h4 className="text-base font-medium">{field.label}</h4>
        <div className="space-y-6">
          {sectionField.fields &&
            Array.from(
              { length: Math.ceil(sectionField.fields.length / 3) },
              (_, groupIndex) => {
                const startIndex = groupIndex * 3;
                const textareaField = sectionField.fields![startIndex];
                const importanceField = sectionField.fields![startIndex + 1];
                const confidenceField = sectionField.fields![startIndex + 2];

                if (!textareaField || !importanceField || !confidenceField)
                  return null;

                const TextareaComponent = fieldComponents[textareaField.type];
                const ImportanceComponent =
                  fieldComponents[importanceField.type];
                const ConfidenceComponent =
                  fieldComponents[confidenceField.type];

                if (
                  !TextareaComponent ||
                  !ImportanceComponent ||
                  !ConfidenceComponent
                ) {
                  return null;
                }

                return (
                  <div key={`group-${groupIndex}`} className="space-y-3">
                    <div>
                      <TextareaComponent
                        name={`${name}.fields.${startIndex}.value`}
                        field={textareaField}
                        control={control}
                        watch={watch}
                        setValue={setValue}
                        readonly={readonly}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-sm font-medium text-gray-700">
                        {importanceField.label}
                      </div>
                      <div className="text-sm font-medium text-gray-700">
                        {confidenceField.label}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <ImportanceComponent
                          name={`${name}.fields.${startIndex + 1}.value`}
                          field={{ ...importanceField, label: '' }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                        />
                      </div>
                      <div>
                        <ConfidenceComponent
                          name={`${name}.fields.${startIndex + 2}.value`}
                          field={{ ...confidenceField, label: '' }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                        />
                      </div>
                    </div>
                  </div>
                );
              }
            )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-base font-medium">{field.label}</h4>
      {sectionField.fields && sectionField.fields.length > 0 && (
        <div className="space-y-4">
          {sectionField.fields.map((subField, index) => {
            const FieldComponent = fieldComponents[subField.type];
            if (!FieldComponent) {
              console.warn(
                `No renderer found for field type: ${subField.type}`
              );
              return null;
            }

            const fieldName =
              subField.type === 'table'
                ? `${name}.fields.${index}`
                : `${name}.fields.${index}.value`;

            return (
              <div key={subField.id} className="ml-4">
                <FieldComponent
                  name={fieldName}
                  field={subField}
                  control={control}
                  watch={watch}
                  setValue={setValue}
                  readonly={readonly}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const fieldComponents: Record<
  LifestyleFields,
  React.FC<FieldComponentProps>
> = {
  radio: RadioField,
  slider: SliderField,
  number: TextField,
  conditional: ConditionalField,
  text: TextField,
  table: TableField,
  textarea: TextareaField,
  section: SectionField,
  grouped_table: GroupedTableField,
  time_range: TextField,
} as const;
