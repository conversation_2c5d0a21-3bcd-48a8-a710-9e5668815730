import dynamic from 'next/dynamic';

import appMetadata from '@/core/configs/app-metadata';
import Loading from '@/lib/common/loading';

// This is a server component that only handles metadata
export const metadata = appMetadata.emr;

// Dynamically import the client component with SSR disabled
const EmrLayoutClient = dynamic(
  () => import('@/core/layout/emr/EmrLayoutWrapper'),
  { ssr: false, loading: () => <Loading /> }
);

// This is the main layout component that will be used by Next.js
export default function EmrLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return <EmrLayoutClient>{children}</EmrLayoutClient>;
}
