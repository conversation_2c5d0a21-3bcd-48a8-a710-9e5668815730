import { create } from 'zustand';

import { modal } from '@/hooks/useModal';

import {
  createPatient,
  getPatientById,
  updatePatient,
} from '@/query/mrd/manage-patient/manage';

import { getErrorMessage } from '@/utils/error-message';

import { Patient } from '@/types/mrd/manage-patient/patient';

import { useMrdPatientSearch } from '../patient-search';

type ManagePatientStoreState = {
  currentTab: number;
  updating: boolean;
  patient: Patient | null;
  loading: boolean;
};

type ManagePatientStoreActions = {
  setCurrentTab: (tab: number) => void;
  getPatientById: (id: string) => Promise<Patient | null>;
  createPatient: (data: Patient) => Promise<void>;
  updatePatient: (data: Patient) => Promise<void>;
  setUploading: (updating: boolean) => void;
  reset: () => void;
};

const initialState: ManagePatientStoreState = {
  currentTab: 0,
  updating: false,
  patient: null,
  loading: false,
};

type ManagePatientStore = ManagePatientStoreState & ManagePatientStoreActions;

export const useManagePatientStore = create<ManagePatientStore>((set) => ({
  ...initialState,
  setCurrentTab: (tab: number) => set({ currentTab: tab }),

  getPatientById: async (id: string) => {
    set({ loading: true });
    try {
      const patient = await getPatientById(id);
      set({ patient });
      return patient;
    } catch (error) {
      console.error(error);
      return null;
    } finally {
      set({ loading: false });
    }
  },

  createPatient: async (data) => {
    set({ updating: true });
    try {
      const response = await createPatient(data);
      useMrdPatientSearch.getState().selectPatient(response.data);
      modal.success('Patient created successfully');
    } catch (error) {
      console.error(error);
      modal.error(getErrorMessage(error, 'Failed to create patient'));
    } finally {
      set({ updating: false, currentTab: 0 });
    }
  },

  updatePatient: async (data) => {
    set({ updating: true });
    try {
      const response = await updatePatient(data);
      useMrdPatientSearch.getState().selectPatient(response.data);
      modal.success('Patient updated successfully');
    } catch (error) {
      console.error(error);
      modal.error(getErrorMessage(error, 'Failed to update patient'));
    } finally {
      set({ updating: false, currentTab: 0 });
    }
  },

  setUploading: (updating) => set({ updating }),

  reset: () => set(initialState),
}));
