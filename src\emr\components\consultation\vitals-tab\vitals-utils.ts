import { VitalAnalyticsValue } from '@/helpers/vitals';

import {
  VitalCardAttributes,
  VitalStatus,
  VitalImprovement,
} from '@/components/vitals-card/types';

export const getVitalValue = (vitalRecord: any, normalizedKey: string) => {
  switch (normalizedKey) {
    case 'respirationRate':
      return vitalRecord.respiratoryRate || vitalRecord.rr;
    case 'bodyMassIndex':
      return vitalRecord.bmi;
    case 'heartRate':
      return vitalRecord.heartRate;
    case 'pulse':
      return vitalRecord.pulse;
    case 'bloodPressure':
    case 'bp':
      return vitalRecord?.sbp && vitalRecord?.dbp
        ? `${vitalRecord.sbp}/${vitalRecord.dbp}`
        : vitalRecord.bloodPressure;
    case 'height':
      return vitalRecord.height;
    case 'weight':
      return vitalRecord.weight;
    case 'spO2':
    case 'spo2':
    case 'oxygenSaturationSpo2':
      return vitalRecord.spO2 || vitalRecord.spo2 || vitalRecord.SpO2;
    case 'temperature':
      return vitalRecord.temperature;
    case 'waistCircumference':
      return vitalRecord.waistCircumference;
    case 'pp':
    case 'pulsePressure':
    case 'pulsePresure':
    case 'pulsePressureMmhg':
      if (vitalRecord?.sbp && vitalRecord?.dbp) {
        const systolic = parseFloat(vitalRecord.sbp);
        const diastolic = parseFloat(vitalRecord.dbp);
        if (!isNaN(systolic) && !isNaN(diastolic)) {
          return (systolic - diastolic).toString();
        }
      }
      return (
        vitalRecord.pp || vitalRecord.pulsePressure || vitalRecord.pulsePresure
      );
    default:
      return vitalRecord[normalizedKey];
  }
};

export const getVitalImprovement = (
  info?: VitalAnalyticsValue
): VitalImprovement | undefined => {
  if (info?.type === 'trend') {
    switch (info.trend) {
      case 'inc':
        return 'improved';
      case 'dec':
        return 'worsened';
      case 'flat':
        return 'stable';
      default:
        return undefined;
    }
  }
  return undefined;
};

export const getVitalStatusKey = (topic: string): string => {
  const normalizedTopic = topic.toLowerCase().trim();
  switch (normalizedTopic) {
    case 'height':
    case 'ht':
      return 'height';

    case 'weight':
    case 'wt':
      return 'weight';

    case 'bmi':
    case 'body mass index':
      return 'bmi';

    case 'pulse':
    case 'heart rate':
    case 'heartrate':
    case 'hr':
      return 'pulse';

    case 'rr':
    case 'respiratory rate':
    case 'respiration rate':
      return 'rr';

    case 'bp':
    case 'blood pressure':
    case 'bloodpressure':
      return 'bp';

    case 'temperature':
    case 'temp':
    case 't':
      return 'temperature';

    case 'spo2':
    case 'spo₂':
    case 'oxygen saturation':
    case 'spO2':
      return 'spO2';

    case 'pp':
    case 'pulse pressure':
    case 'pulsepressure':
      return 'pp';

    default:
      return normalizedTopic;
  }
};

export const shouldShowUnit = (topic: string): boolean => {
  const normalizedTopic = topic.toLowerCase().trim();

  return normalizedTopic !== 'bmi' && normalizedTopic !== 'bp';
};

export const convertToVitalCard = (
  data: string | number,
  unit: string,
  topic: string,
  info?: VitalAnalyticsValue,
  vitalStatuses?: Record<string, VitalStatus>
): VitalCardAttributes => {
  const statusKey = getVitalStatusKey(topic);

  let status: VitalStatus = 'normal';
  if (vitalStatuses && vitalStatuses[statusKey]) {
    status = vitalStatuses[statusKey];
  } else if (!data || data === '--') {
    status = 'unknown';
  }

  const displayUnit = shouldShowUnit(topic) ? unit : '';

  return {
    label: topic,
    value: data?.toString() || '--',
    unit: displayUnit,
    status: status,
    improvement: getVitalImprovement(info),
    percentage: info?.type === 'trend' ? info.value : undefined,
    time: info?.type === 'lastRecorded' ? info.value : undefined,
  };
};

export const getAnalyticsKey = (normalizedKey: string): string => {
  switch (normalizedKey) {
    case 'respirationRate':
      return 'respiratoryRate';
    case 'bodyMassIndex':
      return 'bmi';
    default:
      return normalizedKey;
  }
};

export const mapToAnalyticsKeys = (formattedKeys: string[]): string[] => {
  return formattedKeys.map((key) => {
    switch (key) {
      case 'respirationRate':
        return 'respiratoryRate';
      case 'bodyMassIndex':
        return 'bmi';
      default:
        return key;
    }
  });
};
