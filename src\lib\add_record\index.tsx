'use client';

import { useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';

import { maxBy } from 'lodash';
import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { AiOutlineLoading } from 'react-icons/ai';
import { BiSave, BiX } from 'react-icons/bi';
import { toast } from 'sonner';

import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';

import {
  getSpeechToken,
  SummarizeConversationRes,
  summarizeConversation,
} from '@/query/speech';

import { CurrentModal, currentModal } from '@/constants/ambient-listening';

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import SummaryForm from '@/emr/components/consultation/summary-form';
import Transcript from '@/emr/components/transcript';

import RecordConsultation from '../modals/RecordConsultation';

import './styles.scss';

const RecordingLanguage = {
  English: 'en-IN',
  Malayalam: 'ml-IN',
  Tamil: 'ta-IN',
  Kannada: 'kn-IN',
  Telugu: 'te-IN',
  Bengali: 'bn-IN',
  Hindi: 'hi-IN',
} as const;

type LanguageType = (typeof RecordingLanguage)[keyof typeof RecordingLanguage];

const {
  INITIAL,
  RECORD_CONSULTATION,
  LOADING,
  SHOW_SUMMARY,
  LANGUAGE_SELECTION,
} = currentModal;

export type AddRecordProps = {
  onSave?: (data: SummarizeConversationRes['summary']) => void;
  disabled?: boolean;
};

const AddRecord = ({ onSave = () => {}, disabled = false }: AddRecordProps) => {
  const { doctorProfile } = useDoctorStore();

  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();

  const customEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const emrLanguage = customEmrData?.preferred_language_for_ambient_listening;

  const doctorLanguage = Object.entries(RecordingLanguage).find(
    ([key]) => key === emrLanguage
  )?.[1];

  const [engine, setEngine] =
    useState<speechsdk.ConversationTranscriber | null>(null);
  const [response, setResponse] = useState<SummarizeConversationRes | null>(
    null
  );
  const [currentMode, setCurrentMode] = useState<CurrentModal>(INITIAL);
  const [language, setLanguage] = useState<LanguageType>(
    RecordingLanguage.English
  );

  const form = useForm<SummarizeConversationRes['summary']>();

  const startFlow = () => {
    setCurrentMode(LANGUAGE_SELECTION);
  };

  const handleClose = () => {
    setCurrentMode(INITIAL);
    setLanguage(doctorLanguage || RecordingLanguage.English);
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      handleClose();
    }
  };

  const initializeSpeechEngine = async (language: string) => {
    const speechToken = await getSpeechToken();
    const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
      speechToken,
      'eastus'
    );
    speechConfig.speechRecognitionLanguage = language;

    const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();
    const recognizer = new speechsdk.ConversationTranscriber(
      speechConfig,
      audioConfig
    );
    setEngine(recognizer);
    return recognizer;
  };

  const handleChooseLanguage = (newLanguage: string) => {
    setLanguage(newLanguage as any);
    initializeSpeechEngine(newLanguage);
  };

  const handleTranscript = async (transcript: string) => {
    try {
      setCurrentMode(LOADING);
      const res = await summarizeConversation(transcript);
      setResponse(res);
      setCurrentMode(SHOW_SUMMARY);
    } catch (error) {
      toast.error('Error in summarizing conversation');
      setCurrentMode(LANGUAGE_SELECTION);
      setResponse(null);
      console.error(error);
    }
  };

  const handleSave = form.handleSubmit((submitted) => {
    if (response?.conversation) {
      onSave({
        ...submitted,
        conversation: response?.conversation,
      });
    } else {
      onSave(submitted);
    }

    handleClose();
  });

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  useEffect(() => {
    if (doctorLanguage) {
      setLanguage(doctorLanguage);
    } else {
      setLanguage(RecordingLanguage.English);
    }
  }, [doctorLanguage]);

  useEffect(() => {
    if (currentMode === LANGUAGE_SELECTION) {
      setCurrentMode(LOADING);
      initializeSpeechEngine(language);
      setCurrentMode(LANGUAGE_SELECTION);
    }
  }, [currentMode, language]);

  return (
    <Dialog onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <button
          className={`text-sm flex items-center justify-center gap-1.5 py-1.5 w-full rounded-md ${
            disabled
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-[#4B6BFB] text-white hover:bg-[#3a56d4]'
          }`}
          onClick={!disabled ? startFlow : undefined}
          disabled={disabled}
        >
          Ambient Listening
        </button>
      </DialogTrigger>

      <DialogContent className="flex flex-col p-0 max-h-[95vh] min-h-[65vh] overflow-auto max-w-202 gap-0">
        <section className="mt-5 flex flex-col w-full flex-1">
          {(currentMode === LANGUAGE_SELECTION ||
            currentMode === RECORD_CONSULTATION) && (
            <RecordConsultation
              engine={engine}
              onRecordEnd={handleTranscript}
              handleChooseLanguage={handleChooseLanguage}
              selectedLanguage={language}
              currentMode={currentMode}
              setCurrentMode={setCurrentMode}
            />
          )}

          {currentMode === LOADING && (
            <div className="flex items-center justify-center flex-1 w-full">
              <AiOutlineLoading className="animate animate-spin" />
            </div>
          )}

          {currentMode === SHOW_SUMMARY && (
            <>
              <Tabs
                defaultValue="summary"
                className="w-full flex-grow flex flex-col"
              >
                <DialogHeader className="bg-white sticky top-0 z-10 py-5 pl-6.5 pr-7.5 text-xl font-semibold">
                  <TabsList>
                    <TabsTrigger className="flex-grow" value="summary">
                      <DialogTitle>
                        Consultation Summary (Generated)
                      </DialogTitle>
                    </TabsTrigger>

                    <TabsTrigger className="flex-grow" value="conversation">
                      <DialogTitle>Transcription</DialogTitle>
                    </TabsTrigger>
                  </TabsList>
                </DialogHeader>

                <div className=""></div>

                <TabsContent className="pb-5 pl-6 pr-7.5" value="summary">
                  <SummaryForm data={response} form={form} editable />
                </TabsContent>

                <TabsContent className="flex-grow" value="conversation">
                  <div className="pb-5 pl-6 pr-7.5">
                    {response ? (
                      <Transcript conversation={response?.conversation} />
                    ) : (
                      <div>Transcription not generated</div>
                    )}
                  </div>
                </TabsContent>

                <DialogFooter className="sticky bottom-0 bg-white border-t border-t-[#C2CDD6]">
                  <div className="flex w-full px-6.5 py-5 justify-end">
                    <button
                      className="py-2.5 px-5 border border-black hover:bg-black/5 rounded-full flex items-center gap-[5px]"
                      onClick={handleClose}
                    >
                      Close
                      <BiX />
                    </button>

                    <button
                      className="py-2.5 px-5 bg-black text-white hover:bg-black/80 rounded-full flex items-center gap-[5px] ml-auto"
                      onClick={handleSave}
                    >
                      Save Record
                      <BiSave />
                    </button>
                  </div>
                </DialogFooter>
              </Tabs>
            </>
          )}
        </section>
      </DialogContent>
    </Dialog>
  );
};
export default AddRecord;
