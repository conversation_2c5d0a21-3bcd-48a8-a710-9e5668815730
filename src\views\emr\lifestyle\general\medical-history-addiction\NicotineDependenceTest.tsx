'use client';

import React from 'react';

import { useForm } from 'react-hook-form';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';

interface NicotineDependenceTestProps {
  isOpen: boolean;
  onClose: () => void;
}

type FormValues = {
  timeToFirstCigarette: string;
  findDifficult: string;
  whichCigarette: string;
  cigarettesPerDay: string;
  moreFrequentMorning: string;
  smokeWhenIll: string;
};

const questions = [
  {
    name: 'timeToFirstCigarette',
    questionText: 'How soon after waking do you smoke your first cigarette?',
    options: [
      { label: 'Within 5 minutes', score: 3 },
      { label: '5-30 minutes', score: 2 },
      { label: '31-60 minutes', score: 1 },
    ],
  },
  {
    name: 'findDifficult',
    questionText:
      'Do you find it difficult to refrain from smoking in places where it is forbidden? e.g. Church, Library etc',
    options: [
      { label: 'Yes', score: 1 },
      { label: 'No', score: 0 },
    ],
  },
  {
    name: 'whichCigarette',
    questionText: 'Which cigarette would you hate to give up?',
    options: [
      { label: 'The first in the morning', score: 1 },
      { label: 'Any other', score: 0 },
    ],
  },
  {
    name: 'cigarettesPerDay',
    questionText: 'How many cigarettes do you smoke in a day?',
    options: [
      { label: '31 or more', score: 3 },
      { label: '21-30', score: 2 },
      { label: '11-20', score: 1 },
      { label: '10 or less', score: 0 },
    ],
  },
  {
    name: 'moreFrequentMorning',
    questionText: 'Do you smoke more frequently in morning?',
    options: [
      { label: 'Yes', score: 1 },
      { label: 'No', score: 0 },
    ],
  },
  {
    name: 'smokeWhenIll',
    questionText: 'Do you smoke even if you are sick in bed most of the day',
    options: [
      { label: 'Yes', score: 1 },
      { label: 'No', score: 0 },
    ],
  },
];

const NicotineDependenceTest: React.FC<NicotineDependenceTestProps> = ({
  isOpen,
  onClose,
}) => {
  const { register, watch } = useForm<FormValues>();

  // Calculate total score
  const values = watch();
  const totalScore = React.useMemo(
    () =>
      Object.values(values).reduce((sum, value) => sum + Number(value || 0), 0),
    [values]
  );

  // Determine dependency level

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl p-0 flex flex-col max-h-[90vh]">
        <DialogHeader className="px-6 py-2 border-b">
          <DialogTitle className="text-lg font-semibold">
            Nicotine Dependence Test
          </DialogTitle>
        </DialogHeader>

        <div className="flex-grow overflow-y-auto">
          <table className="w-full text-sm text-left">
            <tbody>
              {questions.map((q, index) => (
                <tr
                  key={q.name}
                  className={`${index % 2 !== 0 ? 'bg-sky-50' : 'bg-white'}`}
                >
                  <td className="px-6 py-4 font-medium text-gray-900 w-1/2 align-top">
                    {q.questionText}
                  </td>
                  <td className="px-6 py-4 w-5/12 align-top">
                    <div className="flex flex-col space-y-3">
                      {q.options.map((option) => (
                        <label key={option.label} className="flex items-center">
                          <input
                            type="radio"
                            {...register(q.name as keyof FormValues)}
                            value={option.score}
                            className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                          />
                          <span className="ml-3 text-gray-700 whitespace-nowrap">
                            {option.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 w-1/12 align-top">
                    <div className="flex flex-col space-y-3">
                      {q.options.map((option) => (
                        <div
                          key={option.label}
                          className="flex items-center h-6"
                        >
                          <span className="text-gray-900 font-medium">
                            {option.score}
                          </span>
                        </div>
                      ))}
                    </div>
                  </td>
                </tr>
              ))}
              <tr className="bg-white border-t">
                <td
                  className="px-6 py-4 font-bold text-right text-gray-900"
                  colSpan={2}
                >
                  Total Score
                </td>
                <td className="px-6 py-4 font-bold text-gray-900">
                  {totalScore}
                </td>
              </tr>
            </tbody>
          </table>

          <div className="bg-sky-200 p-4 m-6 rounded-md">
            <div className="flex">
              <div className="font-semibold pr-12 pt-1 text-gray-800">
                Score
              </div>
              <div className="grid grid-cols-2 gap-x-16 gap-y-1 flex-grow text-gray-700">
                <div>1-2 = Low Dependence</div>
                <div>5-7 = moderate dependence</div>
                <div>3-4 = low-mod dependence</div>
                <div>&gt;8 = high dependence</div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-4 bg-gray-50 border-t sm:justify-start">
          <button
            type="button"
            className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            onClick={() =>
              console.log('Save Record', { ...values, totalScore })
            } // Replace with actual save logic
          >
            Save Record
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NicotineDependenceTest;
