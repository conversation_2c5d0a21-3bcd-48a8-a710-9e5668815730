import { FC, memo, useEffect, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { Questionnaire } from '@/types/emr/lifestyle/questionnaire';

import ExercisePatternForm from './ExercisePatternForm';

type Props = {
  data: Questionnaire;
};

const ExercisePatternTimelineForm: FC<Props> = ({ data }) => {
  const { questions: templateQuestions, getLifestyleQuestions } =
    exercisePatternStore();

  useEffect(() => {
    if (!templateQuestions?.questions?.length) {
      getLifestyleQuestions();
    }
  }, [templateQuestions, getLifestyleQuestions]);

  const methods = useForm<Questionnaire>({
    defaultValues: data,
    mode: 'onChange',
  });

  const formFields = useMemo(() => {
    if (!templateQuestions?.questions?.length) {
      return [];
    }

    return templateQuestions.questions;
  }, [templateQuestions]);

  useEffect(() => {
    if (data) {
      methods.reset(data);
    }
  }, [data, methods]);

  return (
    <FormProvider {...methods}>
      <ExercisePatternForm
        formFields={formFields}
        readonly={true}
        showHeading={false}
      />
    </FormProvider>
  );
};

export default memo(ExercisePatternTimelineForm);
