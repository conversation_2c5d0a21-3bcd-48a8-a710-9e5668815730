'use client';

import { useCurrentPatientStore } from '@/store/currentPatientStore';

import LifestyleSidebar from '@/views/emr/lifestyle/lifestyle-sidebar';

import Chat from '@/emr/components/chat';
import DocAssist from '@/emr/components/doc_assist';
import NoPatientView from '@/emr/components/shared/NoPatientView';

export default function LifestyleLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { patient, isActivePatient } = useCurrentPatientStore();

  if (!patient || !isActivePatient) return <NoPatientView />;

  return (
    <div className="flex gap-2 w-full h-[calc(100vh-4.5rem)] overflow-hidden">
      {/* Left Sidebar - Now Consulting */}
      <div className="w-54 lg:w-62 xl:w-70 h-full max-h-full flex flex-col flex-shrink-0">
        <LifestyleSidebar />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 h-full max-h-full bg-white rounded border overflow-hidden min-w-0">
        {children}
      </div>

      {/* Right Sidebar - Doc Assist */}
      <div className="w-54 lg:w-62 xl:w-70 h-full bg-white rounded border overflow-hidden flex flex-col flex-shrink-0">
        <DocAssist pageId="lifestyle" />
        <Chat />
      </div>
    </div>
  );
}
