import React, { useCallback, useState } from 'react';

import { FiUpload } from 'react-icons/fi';

import { useTestStore } from '@/store/emr/lab/reports-store';

import colors from '@/utils/colors';

import { StatusType } from '@/types/emr/lab';

import UploadFileModal from './UploadFileModal';

interface StatusIndicatorProps {
  status: StatusType;
  id: string;
  fetchTestResult?: () => void;
}

const statusColors: Record<StatusType, string> = {
  Ready: colors.status.ready,
  Awaited: colors.status.awaited,
  'Not Paid': colors.status.notPaid,
  Upload: colors.status.upload,
  Uploaded: colors.status.uploaded,
};

export const getBgColor = (status: StatusType) => {
  return statusColors[status];
};

const statusStyles: Record<StatusType, string> = {
  Ready: `bg-[${statusColors.Ready}] text-white`,
  Awaited: `bg-[${statusColors.Awaited}] text-black`,
  'Not Paid': `bg-[${statusColors['Not Paid']}] text-black`,
  Upload: `bg-transparent text-black cursor-pointer cursor-pointer`,
  Uploaded: `bg-[${statusColors.Uploaded}] text-black`,
};

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  id,
  fetchTestResult,
}) => {
  const [open, setOpen] = useState(false);
  const { fetchRecentTest } = useTestStore();

  const onClick = useCallback(() => {
    if (status === 'Upload' || !status) {
      setOpen(true);
    }
  }, [status]);

  return (
    <>
      <div
        onClick={onClick}
        className={`w-full h-full text-center py-4 font-medium flex justify-center items-center gap-2 ${statusStyles[status ?? 'Upload']}`}
      >
        {(status === 'Upload' || !status) && <FiUpload fontSize="small" />}
        <span className="whitespace-nowrap">{status || 'Upload'}</span>
      </div>
      <UploadFileModal
        open={open}
        onClose={() => setOpen(false)}
        id={id}
        onUploadSuccess={() => {
          fetchRecentTest();
          fetchTestResult?.();
          setOpen(false);
        }}
      />
    </>
  );
};

export default StatusIndicator;
