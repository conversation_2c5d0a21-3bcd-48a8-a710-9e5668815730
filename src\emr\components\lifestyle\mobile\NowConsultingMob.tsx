import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

import { IoIosAdd } from 'react-icons/io';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleUtilStore } from '@/store/lifestyle-utils-store';

import FluentFoodApple20Regular from '@/assets/svg/FluentFoodApple20Regular';

import { calculateAge } from '@/helpers/dates';

import {
  lifestyleModes,
  mobileViews,
  NutritionAndDietForm,
} from '@/constants/lifestyle';

import PatientCard from '../../patient-card';

import LifestyleNote from '../LifestyleNote';
import LifestyleTabs from '../LifestyleTabs';
import TabPanels from '../TabPanels';

import DetailsTimelineButtons from './shared/DetailsTimelineButtons';
import MobilePageTitle from './shared/MobilePageTitle';

const NowConsultingMob = () => {
  const {
    setCurrentTab,
    currentTab,
    setMobilePage,
    setFormMode,
    closeModal,
    isSubmittedToday,
  } = useLifestyleUtilStore();
  const { patient } = useCurrentPatientStore();
  const buttonContainerRef = useRef<HTMLDivElement>(null);

  const [buttonContainerHeight, setButtonContainerHeight] = useState(0);

  const tabList = useMemo(
    () => [
      {
        label: 'Nutrition & Diet',
        icon: <FluentFoodApple20Regular color="inherit" />,
        content: (
          <TabPanels
            items={NutritionAndDietForm}
            activeForm={currentTab}
            onSelectTab={setCurrentTab}
          />
        ),
      },
      // TODO: Enable these when ready
      // {
      //   label: 'Physical Activity',
      //   icon: <RunningIcon />,
      //   content: <>Physical Activity</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Mental Health',
      //   icon: <BrainIcon />,
      //   content: <>Mental Health</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Sleep',
      //   icon: <SleepIcon />,
      //   content: <>Sleep</>,
      //   disabled: true,
      // },
      // {
      //   label: 'Meditation',
      //   icon: <MeditationIcon />,
      //   content: <>Meditation</>,
      //   disabled: true,
      // },
    ],
    [currentTab, setCurrentTab]
  );

  useEffect(() => {
    if (buttonContainerRef.current) {
      setButtonContainerHeight(buttonContainerRef.current.clientHeight + 10);
    }
  }, []);

  return (
    <div className="flex flex-col h-full px-2">
      <MobilePageTitle title="Now Consulting" />
      <DetailsTimelineButtons />
      <div className="pb-4">
        <PatientCard
          name={patient?.name ?? '--'}
          dob={patient?.dob}
          sex={patient?.sex ?? '--'}
          address={patient?.address ?? '--'}
          calculateAge={calculateAge}
        />
      </div>
      <div className="flex flex-col h-[calc(100vh-22rem)] overflow-x-auto">
        <LifestyleTabs items={tabList} />
        <div
          className="flex-1"
          style={{ paddingBottom: `${buttonContainerHeight}px` }}
        >
          <div className="w-full pt-4">
            <LifestyleNote />
          </div>
        </div>
      </div>
      <div
        className="fixed bottom-18 left-0 right-0 py-2 px-3 shadow-lg z-10"
        ref={buttonContainerRef}
      >
        <div className="flex gap-2 items-stretch justify-between">
          <button
            disabled={isSubmittedToday}
            onClick={() => {
              setMobilePage(mobileViews.ADD_RECORD_MANUALLY);
              setFormMode?.(lifestyleModes.CREATE);
              closeModal();
            }}
            className="flex flex-1 gap-1 p-2 items-center text-[15px] justify-center font-semibold bg-primary text-white rounded-lg h-full focus:shadow-custom-xs disabled:bg-gray-300 disabled:opacity-90 disabled:cursor-not-allowed"
          >
            Add Record Manually <IoIosAdd className="text-lg" />
          </button>
          {/* TODO: Add Ambient Listening */}
          {/* <button
            onClick={() => setMobilePage(mobileViews.AMBIENT_LISTENING)}
            disabled={true}
            className="flex flex-1 gap-1 py-2 px-3 items-center justify-center font-semibold text-[15px] rounded-lg text-white focus:shadow-custom-xs"
            style={{ backgroundColor: colors.common.azureBlue }}
          >
            Ambient Listening <IoIosAdd className="text-lg" />
          </button> */}
        </div>
      </div>
    </div>
  );
};

export default memo(NowConsultingMob);
