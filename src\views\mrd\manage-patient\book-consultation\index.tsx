'use client';

import React, { FC, memo, useEffect, useCallback, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { BiCheck } from 'react-icons/bi';
import { MdOutlineKeyboardArrowRight } from 'react-icons/md';

import { useRouter } from 'next/navigation';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';
import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { addDateAndTime, formatDate } from '@/utils/dateUtils/dayUtils';
import { getAppointmentId } from '@/utils/mrd/manage-patient/get-appointment-id';

import {
  AppointmentStatus,
  ConsultationView,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';
import { routes } from '@/constants/routes';

import AppButton from '@/core/components/app-button';
import Loader from '@/core/components/app-loaders/Loader';
import AppTitle from '@/core/components/app-title';
import {
  ConsultationForm,
  defaultConsultation,
} from '@/types/mrd/manage-patient/consultation';

import KeyValuePair from '../shared/KeyValuePair';

import BookConsultationForm from './BookConsultationForm';
import BookConsultationPreview from './BookConsultationPreview';
import { consultationSchema } from './validation';

type Props = {
  id: string;
};

const BookConsultation: FC<Props> = ({ id }) => {
  const router = useRouter();
  const {
    patient,
    getPatientById,
    loading: patientLoading,
  } = useManagePatientStore();

  const {
    fetchAllDoctors,
    setView,
    view,
    createAppointments,
    updatingAppointment,
    getFutureAppointments,
    appointments,
    doctors,
    reset: resetStore,
    futureAppointmentsLoading,
  } = useBookConsultationStore();

  const methods = useForm<{ consultation: ConsultationForm[] }>({
    defaultValues: { consultation: [defaultConsultation] },
    resolver: yupResolver(consultationSchema) as any,
  });

  const { handleSubmit, trigger, reset } = methods;

  const consultationComponent = useMemo(() => {
    switch (view) {
      case ConsultationView.form:
        return <BookConsultationForm />;
      case ConsultationView.preview:
        return <BookConsultationPreview />;
      default:
        return <BookConsultationForm />;
    }
  }, [view]);

  const handleClickCancel = useCallback(() => {
    router.push(routes.MRD_MANAGE_PATIENTS);
  }, [router]);

  const onSubmit = useCallback(
    async (data: { consultation: ConsultationForm[] }) => {
      try {
        const consultation = data.consultation.map((item) => ({
          ...item,
          patientId: patient?.id ?? '',
          doctorId: item.doctorId?.id ?? '',
          status:
            `${AppointmentStatus.Booked}-${PatientStatus.Booked}` as const,
          date: addDateAndTime(item.date, item.time),
          id: item?.id ?? getAppointmentId(),
        }));
        await createAppointments(consultation);
        handleClickCancel();
      } catch (error) {
        console.error(error);
      }
    },
    [createAppointments, patient?.id, handleClickCancel]
  );

  const handleClickNext = useCallback(async () => {
    const isValid = await trigger();
    handleSubmit((_data) => {})();
    if (!isValid) return;
    setView(ConsultationView.preview);
  }, [setView, trigger, handleSubmit]);

  const handleClickPrevious = useCallback(() => {
    setView(ConsultationView.form);
  }, [setView]);

  const handleClickConfirmBooking = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  const handleReset = useCallback(async () => {
    if (appointments.length === 0) {
      reset({ consultation: [defaultConsultation] });
    } else {
      const formattedConsultation = appointments.map((item) => {
        return {
          ...item,
          id: item?.appointmentId,
          doctorId: doctors.find((doc) => doc.id === item.doctorId) ?? null,
          date: formatDate(item.date, DateFormats.DATE_YYYY_MM_DD),
          type: item?.type ?? 'new',
        };
      });

      reset({ consultation: formattedConsultation });
    }
  }, [appointments, reset, doctors]);

  useEffect(() => {
    if (id) {
      getPatientById(id);
      getFutureAppointments(id);
    }
  }, [id, getPatientById, getFutureAppointments]);

  useEffect(() => {
    fetchAllDoctors();
  }, [fetchAllDoctors]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => resetStore(), [resetStore]);

  return (
    <FormProvider {...methods}>
      <form
        className="w-full h-full flex flex-col"
        onSubmit={handleSubmit(onSubmit)}
      >
        <AppTitle className="pb-base">Book Consultation</AppTitle>
        <div className="flex gap-base py-base border-b w-full">
          <div className="flex gap-base w-1/2">
            <KeyValuePair
              label="Name"
              value={patient?.name}
              loading={patientLoading}
            />
            <KeyValuePair
              label="Patient ID"
              value={patient?.id}
              loading={patientLoading}
            />
            <KeyValuePair
              label="Last Visited"
              loading={patientLoading}
              value={formatDate(
                patient?.last_consultation_date,
                DateFormats.DATE_DD_MM_YYYY_SLASH
              )}
            />
          </div>
        </div>
        <div className="flex-1 w-full h-full overflow-y-auto">
          {futureAppointmentsLoading ? (
            <div className="flex items-center justify-center h-full w-full">
              <Loader />
            </div>
          ) : (
            consultationComponent
          )}
        </div>
        <div className="flex gap-base w-full justify-end">
          {view === ConsultationView.form ? (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              variant="outlined"
              onClick={handleClickCancel}
            >
              Cancel
            </AppButton>
          ) : (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              variant="outlined"
              onClick={handleClickPrevious}
            >
              Reschedule
            </AppButton>
          )}
          {view === ConsultationView.form ? (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              onClick={handleClickNext}
              endIcon={<MdOutlineKeyboardArrowRight />}
            >
              Next
            </AppButton>
          ) : (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              onClick={handleClickConfirmBooking}
              endIcon={<BiCheck />}
              loading={updatingAppointment}
            >
              Confirm Booking
            </AppButton>
          )}
        </div>
      </form>
    </FormProvider>
  );
};

export default memo(BookConsultation);
